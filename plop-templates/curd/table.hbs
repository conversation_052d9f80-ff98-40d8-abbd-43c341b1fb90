<template>
  <div class="{{ dashCase name }}-container">
    <vab-query-form>
      <vab-query-form-left-panel :span="12">
        <el-button :icon="Plus" type="primary" @click="handleEdit">
          添加
        </el-button>
        <el-button :icon="Delete" type="danger" @click="handleDelete">
          批量删除
        </el-button>
      </vab-query-form-left-panel>
      <vab-query-form-right-panel :span="12">
        <el-form :inline="true" :model="queryForm" @submit.prevent>
          <el-form-item>
            <el-input
              v-model.trim="queryForm.title"
              clearable
              placeholder="按回车键搜索"
              @keyup.enter="fetchData"
            />
          </el-form-item>
        </el-form>
      </vab-query-form-right-panel>
    </vab-query-form>

    <vab-table ref="tableRef" :columns="columns" :request="loadData">
      <template #oper="{ row }">
        <el-button
          type="primary"
          @click="handleEdit(row)"
          :icon="EditIcon"
          size="small"
        />
        <el-button
          type="danger"
          @click="handleDelete(row)"
          :icon="Delete"
          size="small"
        />
      </template>
    </vab-table>

    <edit ref="editRef" @fetch-data="fetchData" />
  </div>
</template>

<script setup>
  import { getList, doDelete } from '@/api/{{ camelCase name }}'
  import Edit from './components/{{ properCase name }}Edit'
  import { Edit as EditIcon, Delete, Plus, Search } from '@element-plus/icons-vue'

  const $baseConfirm = inject('$baseConfirm')
  const $baseMessage = inject('$baseMessage')

  const columns = ref([
    { label: 'id', prop: 'ID' },
    { label: '标题', prop: 'title' },
    { label: '操作', tdSlot: 'oper' },
  ])
  const queryForm = reactive({ title: '' })
  const loadData = async (params) => {
    const p = {
      ...queryForm,
      ...params,
    }
    const { data } = await getList(p)
    return {
      data: data,
    }
  }

  const editRef = ref()
  const handleEdit = (row) => {
    if (row.id) {
      editRef.value.showEdit(row)
    } else {
      editRef.value.showEdit()
    }
  }

  const selectRows = ref([])
  const handleDelete = (row) => {
    if (row.id) {
      $baseConfirm('你确定要删除当前项吗', null, async () => {
        const { msg } = await doDelete({ ids: row.id })
        $baseMessage(msg, 'success', 'vab-hey-message-success')
        await fetchData()
      })
    } else {
      if (selectRows.value.length > 0) {
        const ids = state.value.map((item) => item.id).join()
        $baseConfirm('你确定要删除选中项吗', null, async () => {
          const { msg } = await doDelete({ ids })
          $baseMessage(msg, 'success', 'vab-hey-message-success')
          await fetchData()
        })
      } else {
        $baseMessage('未选中任何行', 'error', 'vab-hey-message-error')
      }
    }
  }

  const tableRef = ref() //表格原型对象
  const fetchData = async () => {
    tableRef.value.refresh()
  }
</script>
