import request from '@/utils/request'

export function getList(params: any) {
  return request({
    url: '/{{ name }}/getList',
    method: 'get',
    params,
  })
}

export function doEdit(data: any) {
  return request({
    url: '/{{ name }}/doEdit',
    method: 'post',
    data,
  })
}

export function doDelete(data: any) {
  return request({
    url: '/{{ name }}/doDelete',
    method: 'post',
    data,
  })
}
