<template>
  <vab-card shadow="never" style="padding: 0; margin-bottom: 0px; border: none">
    <div style="height: 100%">
      <el-row :gutter="12" style="padding: 0; margin-right: 0px">
        <!-- 左侧目录树 -->
        <el-col
          :lg="state.el_col_left_lg"
          :md="12"
          :sm="24"
          :xl="state.el_col_left_lg"
          :xs="24"
          style="
            min-width: 300px;
            padding: 16px 2px 0px 16px;
            border-right: 1px solid #dcdfe6;
          "
        >
          <div style="overflow: hidden">
            <div class="bi-custom-card-wiki">
              <div class="bi-wiki-navigation">
                <div
                  class="bi-wiki-nav-item"
                  :class="{ active: activePage === 'space' }"
                  @click="pageListModuleClick"
                >
                  <el-icon><List /></el-icon>
                  <span>页面列表</span>
                </div>
                <!-- <div
                  class="bi-wiki-nav-item"
                  :class="{ active: activePage === 'search' }"
                  @click="activePage = 'search'"
                >
                  <el-icon><Search /></el-icon>
                  <span>搜索页面</span>
                </div> -->
              </div>

              <div
                style="
                  display: flex;
                  align-items: center;
                  justify-content: space-between;
                  width: 100%;
                  line-height: 2.5em;
                "
              >
                <div style="flex: 1; color: #999">目录/页面</div>
                <div
                  style="
                    display: flex;
                    align-items: center;
                    margin-right: 12px;
                    cursor: pointer;
                  "
                  v-if="hasEditPermission"
                >
                  <el-popover
                    placement="bottom-start"
                    :width="120"
                    trigger="click"
                    popper-class="wiki-content-el-popover-self"
                    class="bi-hoverable-item-wiki-option-hoverbox"
                  >
                    <template #reference>
                      <el-icon size="20" style="color: #3977f7">
                        <CirclePlusFilled />
                      </el-icon>
                    </template>

                    <div
                      class="bi-hoverable-item-wiki"
                      @click="newWikiCatalog(null)"
                    >
                      新建文件夹
                    </div>
                    <div
                      class="bi-hoverable-item-wiki"
                      @click="newWikiDocument()"
                    >
                      新建文档
                    </div>
                  </el-popover>
                </div>
              </div>

              <div
                class="bi-wiki-tree-list"
                :class="{ 'no-edit-permission': !hasEditPermission }"
              >
                <a-tree
                  v-if="state.showATree"
                  :draggable="hasEditPermission"
                  ref="treeRef"
                  :tree-data="state.data"
                  :height="state.aTreeHeight"
                  :field-names="{ key: 'node_id', title: 'title' }"
                  style="
                    padding-right: 10px;
                    margin-right: 2px;
                    font-family: 'PingFang SC', Arial, 'Microsoft YaHei',
                      sans-serif;
                  "
                  auto-expand-parent
                  default-expand-all
                  :expanded-keys="Array.from(expandedKeys)"
                  :selected-keys="selectedKeys"
                  :block-node="true"
                  @select="openWikiContent"
                  @expand="handleExpand"
                  @drop="handleDrop"
                >
                  <!-- 自定义 switcherIcon -->
                  <template #switcherIcon="{ expanded }">
                    <el-icon
                      class="custom-switcher-icon"
                      :style="{
                        transform: expanded ? 'rotate(90deg)' : 'rotate(0deg)',
                        color: '#666666',
                      }"
                    >
                      <ArrowRight />
                    </el-icon>
                  </template>
                  <template #title="scope">
                    <div
                      class="bi-tree-node-content"
                      style="display: inline-flex"
                      :style="{ 'max-width': 'calc(100%)' }"
                    >
                      <div>
                        <vab-icon
                          v-if="!scope.data.doc_id"
                          :icon="'icon-dir'"
                          is-custom-svg
                          :style="{
                            width: 16 + 'px',
                            height: 16 + 'px',
                            marginRight: '6px',
                          }"
                        />
                        <vab-icon
                          v-else
                          :icon="'text_content'"
                          is-custom-svg
                          :style="{
                            width: 20 + 'px',
                            height: 20 + 'px',
                            color: '#99999980',
                            marginRight: '4px',
                            marginLeft: '-2px',
                          }"
                        />

                        <!-- 置顶图标 -->
                        <span>
                          <!-- <el-icon
                            v-if="scope.data.is_pinned && scope.data.doc_id"
                            class="wiki-content-pin-icon"
                            color="#ff6b00"
                            size="16"
                            :title="`置顶时间：${
                              scope.data.pinned_at || ''
                            }\n置顶原因：${scope.data.pinned_reason || '无'}`"
                            style="transform: translateY(4px)"
                          >
                            <Star />
                          </el-icon> -->

                          <vab-icon
                            v-if="scope.data.is_pinned && scope.data.doc_id"
                            style="height: 15px; width: 15px"
                            icon="is_top"
                            is-custom-svg
                          />
                          <vab-icon
                            v-if="
                              scope.data.premium_status &&
                              scope.data.premium_status.status_value == 1
                            "
                            style="height: 15px; width: 15px"
                            icon="is_premium"
                            is-custom-svg
                          />
                        </span>

                        <showTip
                          :content="scope.data.title"
                          :max-width="
                            getVisiableTooltipWidth(scope.data) + 'px'
                          "
                          :padding-top="'1px'"
                          placement-direction="left"
                        />
                      </div>

                      <div>
                        <div
                          class="bi-fixed-right-icon"
                          style="display: inline-flex; height: 100%"
                        >
                          <div class="wiki-content-doc-count-container">
                            <div
                              v-if="!scope.data.doc_id"
                              class="wiki-content-doc-count"
                            >
                              {{ scope.data.doc_count }}
                            </div>
                          </div>
                          <div
                            class="bi-hoverable-item-wiki-option-container"
                            v-if="hasEditPermission"
                          >
                            <el-popover
                              placement="bottom-start"
                              trigger="hover"
                              width="120"
                              popper-class="wiki-content-el-popover-self"
                              class="bi-hoverable-item-wiki-option-hoverbox"
                            >
                              <template #reference>
                                <vab-icon
                                  style="
                                    width: 24px;
                                    height: 24px;
                                    margin-top: -4px;
                                    margin-bottom: -2px;
                                  "
                                  class="bi-hoverable-item-wiki-option-icon"
                                  is-custom-svg
                                  icon="more"
                                  @click.stop=""
                                />
                              </template>

                              <div v-if="scope.data.doc_id">
                                <div
                                  class="bi-hoverable-item-wiki"
                                  @click="copyWikiLink(scope.data)"
                                >
                                  复制链接
                                </div>
                                <div
                                  class="bi-hoverable-item-wiki"
                                  @click="moveDocumentToSpace(scope.data)"
                                >
                                  移动到空间
                                </div>
                                <!-- 文档权限设置菜单 -->
                                <div
                                  class="bi-hoverable-item-wiki"
                                  @click="openPrivacyDialog(scope.data)"
                                >
                                  文档权限
                                </div>
                                <!-- 置顶相关菜单 -->
                                <div
                                  v-if="
                                    !scope.data.is_pinned && hasPinPermission
                                  "
                                  class="bi-hoverable-item-wiki"
                                  @click="pinDocument(scope.data)"
                                >
                                  置顶文档
                                </div>
                                <div
                                  v-else-if="
                                    scope.data.is_pinned && hasPinPermission
                                  "
                                  class="bi-hoverable-item-wiki"
                                  @click="unpinDocument(scope.data)"
                                >
                                  取消置顶
                                </div>
                                <div
                                  class="bi-hoverable-item-wiki color-danger"
                                  @click="wikiPageDelete(scope.data)"
                                >
                                  删除页面
                                </div>
                              </div>
                              <div v-else>
                                <div
                                  class="bi-hoverable-item-wiki"
                                  @click="newWikiCatalog(scope.data)"
                                >
                                  新建子文件夹
                                </div>
                                <div
                                  class="bi-hoverable-item-wiki"
                                  @click="newWikiPage(scope.data)"
                                >
                                  新建文档
                                </div>
                                <div
                                  class="bi-hoverable-item-wiki"
                                  @click="openCatalogSettings(scope.data)"
                                >
                                  设置
                                </div>
                                <div
                                  class="bi-hoverable-item-wiki"
                                  @click="openProjectWikiImport(scope.data)"
                                >
                                  项目Wiki导入
                                </div>
                                <div
                                  class="bi-hoverable-item-wiki"
                                  @click="exportCatalog(scope.data)"
                                >
                                  导出为zip
                                </div>
                                <div
                                  class="bi-hoverable-item-wiki color-danger"
                                  @click="wikiCatalogDelete(scope.data)"
                                >
                                  删除目录
                                </div>
                              </div>
                            </el-popover>
                          </div>
                        </div>
                      </div>
                    </div>
                  </template>
                </a-tree>
              </div>
            </div>
          </div>
        </el-col>

        <!-- 右侧内容区 -->
        <el-col
          :lg="state.el_col_right_lg"
          :md="12"
          :sm="24"
          :xl="state.el_col_right_lg"
          :xs="24"
          style="flex: 1; min-width: 680px; padding: 0"
        >
          <div v-if="activePage === 'space'" style="padding: 10px 40px">
            <el-tabs v-model="spaceTabActiveName">
              <el-tab-pane label="最近更新" name="recentUpdate">
                <WikiRecentUpdateTable
                  :space-id="currentSpaceId"
                  @open-wiki-content-by-doc-data="openWikiContentByDocData"
                />
              </el-tab-pane>
              <el-tab-pane label="所有文档" name="allDocuments">
                <WikiAllDocument
                  :space-id="currentSpaceId"
                  @open-wiki-content-by-doc-data="openWikiContentByDocData"
                  :current-user-role="state.current_user_role"
                  :from="'-wiki-content'"
                />
              </el-tab-pane>
            </el-tabs>
          </div>

          <!-- 使用抽象出的内容编辑器组件 -->
          <wiki-content-editor
            v-if="activePage === ''"
            ref="contentEditorRef"
            :current-user-role="state.current_user_role"
            :wiki-title="state.wikiTitle"
            :wiki-doc-id="state.wikiDocId"
            :wiki-node="state.flattenWikiList[state.wikiDocId]"
            :space-id="+currentSpaceId"
            :edit-first-mode="state.editFirstMode"
            :wiki-in-creating="state.wikiInCreating"
            :in-edit-mode="state.inEditMode"
            :content-loading="state.wikiContentLoading"
            :editable="state.editable"
            :content="state.mixedText"
            :tags="state.tags"
            v-model:content="state.mixedText"
            :html-mode="state.htmlMode"
            :show-wiki-history="state.showWikiHistory"
            :right-lg-history="state.el_col_right_lg_history"
            :author-info="state.wikiAuthorInfo"
            :updater-info="state.wikiUpdaterInfo"
            :updated-at="
              state.flattenWikiList[state.wikiDocId]?.content_updated_at || ''
            "
            :created-at="
              state.flattenWikiList[state.wikiDocId]?.created_at || ''
            "
            :history-version="
              state.flattenWikiList[state.wikiDocId]?.wiki_contents?.version ||
              1
            "
            :form-disabled="state.formDisabled"
            :editor-id="state.id"
            :save-button-loading="state.saveButtonLoading"
            :comment-count="state.commentCount"
            :upvote-count="state.upvoteCount"
            :audit-value="state.auditValue"
            @audit-success="handleRefreshDocument(state.wikiDocId)"
            @title-changed="wikiTitleChanged"
            @edit-content="handleEditWikiContent"
            @upload-success="handleUploadSuccess"
            @content-change="wikiContentChange"
            @show-user-info="showUserInfo"
            @set-wiki-content="setWikiContent"
            @save-content="(content) => saveWikiContent(content)"
            @delete-content="deleteWikiContent"
            @cancel-edit="handleCancelEdit"
            @image-click="handleImageClick"
            @create-new="newWikiPage"
            @refresh-document="handleRefreshDocument"
            @open-tag-manager="openTagManager"
          />
        </el-col>
      </el-row>
    </div>
    <wiki-cata-log-settings-dialog
      ref="catalogSettingsDialogRef"
      @updated="fetchWikiList"
    />
    <project-wiki-import-dialog
      ref="projectWikiImportDialogRef"
      @import-success="fetchWikiList"
    />
    <wiki-move-document-dialog
      ref="moveDocumentDialogRef"
      :selected-documents="
        state.selectedDocumentForMove.doc_id
          ? [state.selectedDocumentForMove]
          : []
      "
      @move-success="handleDocumentMoveSuccess"
      @move-error="handleDocumentMoveError"
    />
    <el-dialog
      v-model="state.tagDialogVisible"
      title="标签管理"
      :width="1150"
      @close="handleTagDialogClose"
    >
      <wiki-tag-table :height="550" ref="tagTableRef" />
    </el-dialog>
    <firefly-dialog
      v-model="state.newDocDialogVisible"
      title="新建文档"
      width="500px"
      :close-on-click-modal="false"
    >
      <el-form :model="state.newDocForm" label-width="120px" @submit.prevent>
        <el-form-item label="文档标题：" required>
          <el-input
            v-model="state.newDocForm.title"
            placeholder="请输入文档标题"
            @keyup.enter="createNewWikiPage"
          />
        </el-form-item>
        <el-form-item label="编辑器类型：">
          <el-radio-group v-model="state.newDocForm.editorType">
            <el-radio :label="1">富文本</el-radio>
            <el-radio :label="2">Markdown</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="文档权限：">
          <el-radio-group v-model="state.newDocForm.is_public">
            <el-radio :label="1">公开共享</el-radio>
            <el-radio :label="0">成员共享</el-radio>
            <el-radio :label="-1">仅创建者可见</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="state.newDocDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="createNewWikiPage"
            :loading="state.newDocForm.loading"
          >
            确定
          </el-button>
        </span>
      </template>
    </firefly-dialog>

    <!-- 文档权限设置弹窗 -->
    <firefly-dialog
      v-model="state.privacyDialogVisible"
      title="设置文档权限"
      @close="state.privacyDialogVisible = false"
      @confirm="confirmPrivacyChange"
      :width="440"
      class="wiki-document-privacy-dialog"
      show-default-button
      :draggable="false"
    >
      <el-form
        ref="privacyFormRef"
        :model="state.privacyForm"
        :label-position="'top'"
        class="wiki-document-privacy-form"
        @submit.prevent
      >
        <el-form-item label="可见性">
          <el-radio-group v-model="state.privacyForm.is_public">
            <el-radio :label="1">公开共享</el-radio>
            <el-radio :label="0">成员共享</el-radio>
            <el-radio :label="-1">仅创建者可见</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
    </firefly-dialog>
    <WikiPinDialog
      ref="pinDialogRef"
      @pin-success="handlePinSuccess"
      @unpin-success="handleUnpinSuccess"
    />
  </vab-card>
</template>

<script setup>
  import { provide } from 'vue'
  import {
    CirclePlusFilled,
    List,
    Search,
    ArrowRight,
    StarFilled,
    Star,
  } from '@element-plus/icons-vue'
  import showTip from '~/src/views/project/issue/components/overflowTooltip.vue'
  import { useUserStore } from '@/store/modules/user'
  import {
    getList,
    doEdit,
    doDelete,
    getWikiCatalogList,
    doWikiCatalogEdit,
    doWikiCatalogDelete,
    wikiCatalogRearrangement,
    wikiExportCatalogRecursive,
  } from '@/api/biWiki'
  import { getSpaceMemberRole } from '@/api/biWiki'
  import WikiContentEditor from './WikiContentEditor.vue'
  import WikiRecentUpdateTable from '~/src/views/oa/Wiki/components/tables/WikiRecentUpdateTable.vue'
  import WikiAllDocument from '~/src/views/oa/Wiki/components/WikiAllDocument.vue'
  import { roleAuth } from '@/utils/auth'
  import WikiCataLogSettingsDialog from './dialogs/WikiCataLogSettingsDialog.vue'
  import ProjectWikiImportDialog from './dialogs/ProjectWikiImportDialog.vue'
  import WikiTagTable from '~/src/views/oa/Wiki/components/tables/WikiTagTable.vue'
  import WikiMoveDocumentDialog from './dialogs/WikiMoveDocumentDialog.vue'
  import WikiPinDialog from './dialogs/WikiPinDialog.vue'
  import _ from 'lodash'
  import { ConsoleSqlOutlined } from '@ant-design/icons-vue'

  // 路由相关
  const router = useRouter()
  const route = useRoute()

  // 依赖注入
  const $baseMessage = inject('$baseMessage')
  const $baseConfirm = inject('$baseConfirm')
  const $basePrompt = inject('$basePrompt')
  const $pub = inject('$pub')
  const $sub = inject('$sub')
  const $unsub = inject('$unsub')

  // 计算属性：获取当前空间ID，优先使用props，备用使用路由参数
  const currentSpaceId = computed(() => {
    const spaceId = props.spaceId || route.query.space_id
    return spaceId ? Number(spaceId) : null
  })
  // Props 与 Emits
  const props = defineProps({
    spaceId: {
      type: [String, Number],
      required: true,
    },
    spaceInfo: {
      type: Object,
      default: () => {},
    },
  })

  const emit = defineEmits(['update:data', 'current-user-role'])

  // Store
  const userStore = useUserStore()
  const { user_id } = userStore

  // 编辑器类型相关
  const currentEditorType = ref(1) // 当前编辑器类型 1: html富文本, 2: markdown
  provide('currentEditorType', currentEditorType)

  // 组件引用
  const treeRef = ref(null)
  const contentEditorRef = ref(null)
  const catalogSettingsDialogRef = ref(null)
  const projectWikiImportDialogRef = ref(null)
  const tagTableRef = ref(null)
  const moveDocumentDialogRef = ref(null)
  const privacyFormRef = ref(null)
  const pinDialogRef = ref(null)

  // 页面状态
  const activePage = ref('space')
  const spaceTabActiveName = ref('recentUpdate')

  // 路由同步标志，防止循环触发
  const isRouteSyncing = ref(false)

  // 树结构相关
  const treeProps = {
    label: 'title',
    children: 'children',
  }
  const expandedKeys = ref(new Set())
  const selectedKeys = ref([])

  // 组件状态
  const state = reactive({
    // 布局相关
    el_col_left_lg: 4,
    el_col_right_lg: 20,
    el_col_right_lg_history: 0,

    // 搜索相关
    searchText: '',
    slowLoad1: false,

    // 权限相关
    current_user_role: 'visitor',
    auth_role: {
      role: ['Admin', 'Director'],
    },

    // 数据相关
    data: [], // 树形数据
    flattenWikiList: {},
    flattenWikiListTitleAsKey: {},

    // 节点相关
    wikiNodeId: null,

    // 编辑器内容相关
    mixedText: '', // 编辑器内容
    wikiTitle: '',
    wikiDocId: null,
    wikiInCreating: false,

    // 编辑模式相关
    editable: false,
    inEditMode: false,
    htmlMode: true,
    showWikiHistory: false,
    editFirstMode: false,
    formDisabled: false,
    saveButtonLoading: false,
    wikiContentLoading: false,

    // 权限弹窗相关
    privacyDialogVisible: false,
    privacyForm: {
      is_public: 1,
    },
    currentDocumentForPrivacy: null,

    // 用户信息相关
    wikiAuthorInfo: {
      name: '',
      thumb_avatar: '',
      user_id: 0,
    },
    wikiUpdaterInfo: null,

    // 组件ID
    id: `wiki-editor-${Date.now()}`,

    // 查询表单
    queryForm: {
      filter: {
        space_id: null, // 将在组件挂载时设置
        catalog_id: -3,
      },
      op: {},
      page: 1,
      limit: 9999,
      sort: 'created_at',
      order: 'ASC',
    },

    // 图片查看相关
    showImage: false,
    imgIndex: 0,
    srcList: [],

    // 其他
    parentDocId: null,
    commentCount: 0,
    upvoteCount: 0,
    showATree: false,
    aTreeHeight: 0,
    auditValue: 0,

    // 标签管理相关
    tagDialogVisible: false,

    // 添加编辑器类型存储key
    editorTypeStorageKey: 'wiki_last_editor_type',

    // 在state中添加备份相关的key (将在组件挂载时设置)
    backupStorageKey: '',

    // 在 state 中添加存储 key
    expandedKeysStorageKey: 'wiki_expanded_keys', // 用于存储展开状态的 key

    // 新建文档对话框相关
    newDocDialogVisible: false,
    newDocForm: {
      title: '',
      editorType: 1, // 默认使用富文本编辑器
      is_public: 1, // 默认为公开共享
      loading: false,
      catalogId: null,
    },

    // 移动文档相关
    selectedDocumentForMove: {},
  })

  // 提供 flattenWikiList 数据给子组件
  provide('flattenWikiList', readonly(toRef(state, 'flattenWikiList')))

  // 添加全局导出状态控制变量
  const isAnyExporting = ref(false)

  // ===== 权限处理函数 =====
  const hasEditPermission = computed(() => {
    return (
      roleAuth(state.auth_role.role) ||
      state.current_user_role == 'editor' ||
      state.current_user_role == 'owner' ||
      state.current_user_role == 'member'
    )
  })

  // 置顶权限检查函数 - 排除member用户
  const hasPinPermission = computed(() => {
    return (
      roleAuth(state.auth_role.role) ||
      state.current_user_role == 'editor' ||
      state.current_user_role == 'owner'
    )
  })

  // 获取当前成员角色
  const getCurrentMemberRole = async () => {
    const queryForm = {
      space_id: currentSpaceId.value,
      id: user_id,
      associated_field: 'user_id',
    }
    const res = await getSpaceMemberRole(queryForm)
    state.current_user_role = res.data
    emit('current-user-role', state.current_user_role)
  }

  // ===== 树高度计算函数 =====

  // 计算树组件高度
  const calculateTreeHeight = () => {
    const height = window.innerHeight - 168
    state.aTreeHeight = Math.max(height, 300) // 设置最小高度300px
    state.showATree = true
  }

  // 处理窗口大小变化
  const handleResize = () => {
    calculateTreeHeight()
  }

  // ===== 页面交互函数 =====

  // 获取可见tooltip的宽度
  const getVisiableTooltipWidth = (node) => {
    let maxWidth = 166
    // 计算深度：现在path格式为 "/1/5/9/" 这样的形式
    // 统计斜杠数量并减1（因为最后有一个斜杠）即可得到层级深度
    const depth = node.tree_depth
    maxWidth -= depth * 24
    return Math.max(maxWidth, 0)
  }

  // 搜索处理
  const handleSearch = async () => {
    try {
      state.slowLoad1 = true
      const query = { ...state.queryForm }

      if (state.searchText) {
        query.filter.title = state.searchText
        query.op.title = 'like'
      } else {
        delete query.filter.title
        delete query.op.title
      }

      await fetchWikiList(query)
    } finally {
      state.slowLoad1 = false
    }
  }

  // ===== Wiki 文件夹操作函数 =====

  // 新建Wiki文件夹
  const newWikiCatalog = async (node) => {
    if (state.editable && state.inEditMode) {
      $baseMessage('请先保存', 'warning', 'vab-hey-message-warning')
      return
    }
    if (!hasEditPermission.value) {
      $baseMessage('无权限', 'warning', 'vab-hey-message-warning')
      return
    }
    // console.log('newWikiCatalog:', node)
    let form = {
      catalog_id: -1,
      name: '新建文件夹',
      space_id: currentSpaceId.value,
    }

    // 根据空间公开状态决定新目录的默认权限
    const spaceInfo = props.spaceInfo
    // 如果空间是公开的，则新目录默认也是公开的
    form.is_public = spaceInfo?.is_public === 1 ? 1 : 0

    let newChildCatalogPid = null
    if (node) {
      form.pid = node.catalog_id
      newChildCatalogPid = node.catalog_id
    }

    openCatalogSettings(null, newChildCatalogPid)

    // await doWikiCatalogEdit(form)
    // await fetchWikiList()
  }

  // ===== Wiki 页面操作函数 =====

  // 新建wiki页面，可接受父页面参数
  const newWikiPage = async (nodeData) => {
    if (state.editable && state.inEditMode) {
      $baseMessage('请先保存', 'warning', 'vab-hey-message-warning')
      return
    }
    if (!hasEditPermission.value) {
      $baseMessage('无权限', 'warning', 'vab-hey-message-warning')
      return
    }

    // 从localStorage获取上次使用的编辑器类型，默认为1（富文本）
    const lastEditorType = parseInt(
      localStorage.getItem(state.editorTypeStorageKey) || '1'
    )

    // 设置目录ID并显示对话框
    state.newDocForm.catalogId = nodeData.catalog_id
    state.newDocForm.title = '新建页面'
    state.newDocForm.editorType = lastEditorType
    state.newDocForm.is_public = 1 // 默认为公开共享
    state.newDocDialogVisible = true
  }

  // 新建wiki文档，catalog默认为0
  const newWikiDocument = async () => {
    if (state.editable && state.inEditMode) {
      $baseMessage('请先保存', 'warning', 'vab-hey-message-warning')
      return
    }
    if (!hasEditPermission.value) {
      $baseMessage('无权限', 'warning', 'vab-hey-message-warning')
      return
    }

    // 从localStorage获取上次使用的编辑器类型，默认为1（富文本）
    const lastEditorType = parseInt(
      localStorage.getItem(state.editorTypeStorageKey) || '1'
    )

    // 设置目录ID为0并显示对话框
    state.newDocForm.catalogId = 0
    state.newDocForm.title = '新建文档'
    state.newDocForm.editorType = lastEditorType
    state.newDocForm.is_public = 1 // 默认为公开共享
    state.newDocDialogVisible = true
  }

  // 添加创建新文档的函数
  const createNewWikiPage = async () => {
    // 表单验证
    if (!state.newDocForm.title.trim()) {
      $baseMessage('请输入文档标题', 'warning', 'vab-hey-message-warning')
      return
    }

    try {
      state.newDocForm.loading = true

      // 保存编辑器类型到localStorage
      localStorage.setItem(
        state.editorTypeStorageKey,
        state.newDocForm.editorType.toString()
      )

      // 创建文档
      let { data } = await doEdit({
        doc_id: -1,
        title: state.newDocForm.title,
        space_id: currentSpaceId.value,
        catalog_id: state.newDocForm.catalogId,
        current_editor_type: state.newDocForm.editorType,
        is_public: state.newDocForm.is_public,
      })

      // 关闭对话框
      state.newDocDialogVisible = false

      // 重新获取文档列表
      await fetchWikiList()

      // 设置编辑状态
      activePage.value = ''
      state.wikiInCreating = true
      state.inEditMode = true
      state.editable = true
      state.wikiNodeId = data.node_id
      state.wikiDocId = data.doc_id
      state.wikiTitle = data.title
      state.mixedText = ''

      // 设置编辑器类型
      state.htmlMode = state.newDocForm.editorType === 1

      // 选中新创建的节点
      setNodeSelected(data)
      scrollToSelectedNode(data.doc_id)

      // 确保编辑器内容被清空
      await nextTick()
      if (contentEditorRef.value) {
        if (state.htmlMode) {
          contentEditorRef.value.setEditorValue('')
        } else {
          contentEditorRef.value.setEditorValue('')
        }
      }

      // 更新路由，移除标签页参数
      router.push({
        path: '/Oa/Wiki/Detail',
        query: {
          space_id: currentSpaceId.value,
          doc_id: data.doc_id,
        },
      })
      $pub('tchip-bi-wiki-content-created')

      $baseMessage('文档创建成功', 'success', 'vab-hey-message-success')
    } catch (error) {
      console.error('创建文档失败:', error)
      $baseMessage('创建文档失败', 'error', 'vab-hey-message-error')
    } finally {
      state.newDocForm.loading = false
    }
  }

  // 复制Wiki链接
  const copyWikiLink = (data) => {
    // 构建链接
    const text = `${window.location.origin}/#/Oa/Wiki/Detail?space_id=${data.space_id}&doc_id=${data.doc_id}`

    // 使用备用方法复制
    const textarea = document.createElement('textarea')
    textarea.value = text
    textarea.style.position = 'fixed'
    textarea.style.left = '-9999px'
    document.body.appendChild(textarea)
    textarea.select()

    try {
      const success = document.execCommand('copy')
      if (success) {
        $baseMessage('文档链接已复制', 'success', 'vab-hey-message-success')
      } else {
        $baseMessage('复制失败', 'error', 'vab-hey-message-error')
      }
    } catch (err) {
      $baseMessage(`复制失败: ${err}`, 'error', 'vab-hey-message-error')
    } finally {
      document.body.removeChild(textarea)
    }
  }

  // 置顶文档
  const pinDocument = (data) => {
    if (!hasPinPermission.value) {
      $baseMessage('无权限', 'warning', 'vab-hey-message-warning')
      return
    }

    if (pinDialogRef.value) {
      pinDialogRef.value.showDialog('pin', [data])
    }
  }

  // 取消置顶
  const unpinDocument = (data) => {
    if (!hasPinPermission.value) {
      $baseMessage('无权限', 'warning', 'vab-hey-message-warning')
      return
    }

    if (pinDialogRef.value) {
      pinDialogRef.value.showDialog('unpin', [data])
    }
  }

  // 置顶成功回调
  const handlePinSuccess = async (result) => {
    // // 刷新列表
    // await fetchWikiList()
  }

  // 取消置顶成功回调
  const handleUnpinSuccess = async (result) => {
    // // 刷新列表
    // await fetchWikiList()
  }

  const wikiPageDelete = (data) => {
    if (!hasEditPermission.value) {
      $baseMessage('无权限', 'warning', 'vab-hey-message-warning')
      return
    }

    $baseConfirm('确定要删除这个页面吗？删除后将无法恢复。', null, () => {
      doDelete({ ids: [data.doc_id] })
        .then(() => {
          $baseMessage('删除成功', 'success', 'vab-hey-message-success')

          // 刷新列表
          fetchWikiList()

          // 如果正在查看的是被删除的页面，清空内容
          if (route.query.doc_id == data.doc_id) {
            pageListModuleClick()
          }
        })
        .catch((err) => {
          $baseMessage('删除失败', 'error', 'vab-hey-message-error')
          console.error(err)
        })
    })
  }

  // 移动文档到空间
  const moveDocumentToSpace = (data) => {
    if (!hasEditPermission.value) {
      $baseMessage('无权限', 'warning', 'vab-hey-message-warning')
      return
    }

    // 设置要移动的文档数据
    state.selectedDocumentForMove = data

    // 打开移动对话框
    if (moveDocumentDialogRef.value) {
      moveDocumentDialogRef.value.showDialog()
    }
  }

  // 处理文档移动成功
  const handleDocumentMoveSuccess = async (result) => {
    // 刷新wiki列表
    await fetchWikiList()

    // 如果正在查看被移动的文档，跳转到空间首页
    if (route.query.doc_id == state.selectedDocumentForMove.doc_id) {
      pageListModuleClick()
    }

    // 清空选中的文档
    state.selectedDocumentForMove = {}
  }

  // 处理文档移动失败
  const handleDocumentMoveError = (error) => {
    console.error('移动文档失败:', error)
    // 清空选中的文档
    state.selectedDocumentForMove = {}
  }

  // 检查文档权限修改权限
  const hasDocumentPrivacyPermission = (data) => {
    // 系统超管拥有所有权限
    if (roleAuth(['Admin'])) {
      return true
    }

    // 总监Director拥有所有权限
    if (roleAuth(['Director'])) {
      return true
    }

    // 空间所有者拥有所有操作权限
    if (state.current_user_role === 'owner') {
      return true
    }

    // 空间管理员拥有所有操作权限
    if (state.current_user_role === 'editor') {
      return true
    }

    // 文档创建者可以修改自己创建的文档权限
    if (data.created_by && data.created_by === user_id) {
      return true
    }

    // 其他情况无权限
    return false
  }

  // 打开权限设置弹窗
  const openPrivacyDialog = (data) => {
    // 使用新的文档权限检查函数
    if (!hasDocumentPrivacyPermission(data)) {
      $baseMessage('无权限修改文档权限', 'warning', 'vab-hey-message-warning')
      return
    }

    state.currentDocumentForPrivacy = data
    state.privacyForm.is_public = data.is_public
    state.privacyDialogVisible = true
  }

  // 确认权限修改
  const confirmPrivacyChange = async () => {
    if (!state.currentDocumentForPrivacy) {
      return
    }

    const data = state.currentDocumentForPrivacy
    const newPrivacy = state.privacyForm.is_public

    // 如果权限没有变化，直接关闭弹窗
    if (data.is_public === newPrivacy) {
      state.privacyDialogVisible = false
      return
    }

    try {
      await doEdit({
        doc_id: data.doc_id,
        is_public: newPrivacy,
      })

      // 更新本地数据
      data.is_public = newPrivacy

      // 如果有扁平列表数据也需要更新
      if (state.flattenWikiList[data.doc_id]) {
        state.flattenWikiList[data.doc_id].is_public = newPrivacy
      }

      const privacyText = {
        1: '公开',
        0: '组内共享',
        '-1': '私有',
      }

      $baseMessage(
        `文档权限已修改为${privacyText[newPrivacy]}`,
        'success',
        'vab-hey-message-success'
      )

      // 关闭弹窗
      state.privacyDialogVisible = false

      // 刷新列表以确保数据同步
      await fetchWikiList()
    } catch (error) {
      console.error('修改文档权限失败:', error)
      $baseMessage('修改权限失败', 'error', 'vab-hey-message-error')
    }
  }

  // 定义外部状态变量
  let lastExecutionTime = 0 // 上次执行时间
  let executionCount = 0 // 执行计数
  const MAX_EXECUTIONS = 7 // 最大执行次数
  const TIME_WINDOW = 2000 // 时间窗口 2 秒（单位：毫秒）
  const cancelJud = ref(false)

  // 删除目录
  const wikiCatalogDelete = (data) => {
    // console.log('wikiCatalogDelete:', data)
    if (!hasEditPermission.value) {
      $baseMessage('无权限', 'warning', 'vab-hey-message-warning')
      return
    }

    $basePrompt(
      `
      <p style="color:#999999">您正在删除目录：<strong style="overflow-wrap: break-word;background: #99999926;color:#666666";>${data.title}</strong></p>
      <p style="color: #FF5E4B;">删除目录后，目录下所有文档及子目录的数据将一并被删除，请谨慎操作。</p>
      <p style="color: #999999;font-weight: 700">输入您要删除的目录名称以确认删除</p>
      `,
      '确认删除目录', // 标题
      async (inputValue) => {
        // 确认回调
        if (inputValue === data.title || cancelJud.value) {
          try {
            await doWikiCatalogDelete({ ids: [data.catalog_id] })
            // 重置彩蛋状态
            cancelJud.value = false
            $baseMessage('删除成功', 'success', 'vab-hey-message-success')
            fetchWikiList()
          } catch (err) {
            $baseMessage('删除失败', 'error', 'vab-hey-message-error')
            console.error(err)
          }
        }
      },
      (action) => {
        console.log('取消操作:', action) // 取消操作回调
      },
      '确定删除', // 确认按钮文本
      '取消', // 取消按钮文本
      '请输入目录名称确认删除', // 输入框提示文本
      true, // 显示关闭按钮
      'error', // 类型
      true, // 居中
      (value) => {
        const now = Date.now() // 当前时间戳

        // 如果时间窗口已过，重置计数
        if (now - lastExecutionTime > TIME_WINDOW) {
          lastExecutionTime = now
          executionCount = 0
        }

        // 累加执行次数
        executionCount++

        // 判断是否超过限制
        if (executionCount > MAX_EXECUTIONS) {
          console.log('2s内点击8次，跳过输入名称验证')
          cancelJud.value = true
          return true
        }

        // 输入校验逻辑
        if (value === '') {
          return '请输入目录名称'
        }
        return value === data.title ? true : '请输入正确的目录名称'
      },
      true
    )
  }

  // 打开wiki内容
  const openWikiContent = async (selectedKeys, { node }) => {
    if (state.editable && state.inEditMode) {
      $baseMessage('请先保存', 'warning', 'vab-hey-message-warning')
      return
    }
    if (route.query.doc_id == node.doc_id && state.inEditMode) {
      // 如果当前打开的文档就是row，则不进行任何操作
      return
    }

    // 如果 node.doc_id 不存在，则只控制展开/收起
    if (!node.doc_id) {
      // 处理目录展开/收起
      const isExpanded = expandedKeys.value.has(parseInt(node.node_id))
      if (isExpanded) {
        // 如果已展开，则收起
        removeKeysRecursively(node)
      } else {
        // 如果未展开，则展开
        expandedKeys.value.add(parseInt(node.node_id))
      }
      return
    }

    // 以下为原有的文档打开逻辑
    try {
      activePage.value = ''
      // 检查是否有未保存的内容
      if (contentEditorRef.value?.checkUnsavedContent()) {
        window.alert('检测到内容未保存')
        // setNodeSelected(node)
        return
      }

      console.log('node', node)

      state.wikiContentLoading = true
      state.current_user_role = node.current_user_role
      state.wikiTitle = node.title
      state.htmlMode = node.current_editor_type === 1
      state.mixedText =
        (state.htmlMode ? node.content_html : node.content_markdown) ?? ''
      state.wikiInCreating = false
      state.editable = false
      state.inEditMode = false
      state.wikiNodeId = node.node_id
      state.wikiDocId = node.doc_id
      state.commentCount = node.comments_count || 0
      state.upvoteCount = node.upvote_count || 0
      state.tags = node.tags || []

      // 设置作者信息
      state.wikiAuthorInfo = {
        name: node.creator?.name || '未知用户',
        thumb_avatar: node.creator?.thumb_avatar || '',
        user_id: node.created_by || 0,
        user_points: node.creator?.user_points || null,
      }

      state.wikiUpdaterInfo = {
        name: node.content_updater?.name || '未知用户',
        thumb_avatar: node.content_updater?.thumb_avatar || '',
        user_id: node.content_updated_by || 0,
        user_points: node.content_updater?.user_points || null,
      }

      // 更新审核信息
      state.auditValue = node.audit_status?.status_value ?? 0

      // 更新flattenWikiList
      state.flattenWikiList[node.doc_id] = {
        wiki_contents: {
          updated_on: node.updated_at,
          version: node.version || 1,
        },
        ...node,
      }

      state.flattenWikiListTitleAsKey[node.title] = node

      await contentEditorRef.value?.checkOutline()

      // 将选中节点滚动到视图内
      nextTick(() => {
        scrollToSelectedNode(node.doc_id)
      })

      // 构建查询参数，移除标签页参数
      const newQuery = { ...route.query }
      delete newQuery.tab // 打开文档时清除标签页参数
      newQuery.space_id = currentSpaceId.value
      newQuery.doc_id = node.doc_id

      router.push({
        path: '/Oa/Wiki/Detail',
        query: newQuery,
      })

      $pub('wiki-browser-title-change', state.wikiTitle)
      setNodeSelected(node)

      await new Promise((resolve) => setTimeout(resolve, 750))
      state.wikiContentLoading = false
    } catch (error) {
      console.error('打开Wiki内容失败:', error)
      $baseMessage('打开内容失败', 'error', 'vab-hey-message-error')
    }
  }

  /**
   * 展开到指定节点的完整路径
   * @param {Object} targetNode - 目标节点
   */
  const expandPathToNode = (targetNode) => {
    if (!targetNode) return

    // 如果节点有tree_path信息，使用tree_path来确定父级路径
    if (targetNode.tree_path) {
      // tree_path格式通常是 "/1/5/9/" 这样的形式
      const pathParts = targetNode.tree_path
        .split('/')
        .filter((part) => part && part !== '')

      // 展开路径中的所有父级节点
      pathParts.forEach((nodeId) => {
        const numericNodeId = parseInt(nodeId)
        if (numericNodeId && !isNaN(numericNodeId)) {
          expandedKeys.value.add(numericNodeId)
        }
      })
    }

    // 如果有catalog_id，也要确保展开
    if (targetNode.catalog_id && targetNode.catalog_id > 0) {
      expandedKeys.value.add(targetNode.catalog_id)
    }

    // // 递归查找并展开父级节点（备用方案）
    // const findAndExpandParents = (nodeId, data) => {
    //   for (const item of data) {
    //     if (item.node_id === nodeId) {
    //       // 找到目标节点，展开其父级
    //       if (item.pid && item.pid > 0) {
    //         expandedKeys.value.add(item.pid)
    //         findAndExpandParents(item.pid, data) // 递归展开祖先节点
    //       }
    //       return true
    //     }
    //     if (item.children && item.children.length > 0) {
    //       if (findAndExpandParents(nodeId, item.children)) {
    //         expandedKeys.value.add(item.node_id) // 展开当前节点
    //         return true
    //       }
    //     }
    //   }
    //   return false
    // }
    // // 使用节点ID查找父级路径
    // if (targetNode.node_id) {
    //   findAndExpandParents(targetNode.node_id, state.data)
    // }

    // 保存展开状态
    saveExpandedKeys()
  }

  /**
   * 通过wiki文档数据打开wiki内容
   * @param {Object} row - 文档数据
   */
  const openWikiContentByDocData = async (row) => {
    console.log('openWikiContentByDocData')
    // console.log('row', row)
    // console.log('treeRef.value', treeRef.value)

    const node = state.flattenWikiList[row.doc_id]

    if (route.query.doc_id == row.doc_id && state.inEditMode) {
      // 如果当前打开的文档就是row，则不进行任何操作
      return
    }

    if (node) {
      state.wikiNodeId = node.node_id

      // 自动展开到目标文档的父级路径
      expandPathToNode(node)

      setNodeSelected(node)

      // 滚动到选中节点
      await nextTick()
      scrollToSelectedNode(row.doc_id)

      // 构建查询参数，移除标签页参数
      const newQuery = {
        space_id: currentSpaceId.value,
        doc_id: row.doc_id,
      }

      router.push({
        path: '/Oa/Wiki/Detail',
        query: newQuery,
      })
      await nextTick()
      await new Promise((resolve) => setTimeout(resolve, 350))

      openWikiContent(selectedKeys.value, { node })
    }
  }

  const setNodeSelected = (node) => {
    let nodeId = node.node_id ?? node.doc_id * -1
    if (!selectedKeys.value.length || !selectedKeys.value.includes(nodeId)) {
      selectedKeys.value = [nodeId]
    }
  }

  // 滚动到选中节点函数
  const scrollToSelectedNode = async (docId) => {
    // console.log('scrollToSelectedNode', docId)
    await new Promise((resolve) => setTimeout(resolve, 750))

    // 检查节点是否在可视区域内
    const treeContainer = document.querySelector('.ant-tree-list-holder')
    const selectedNode = document.querySelector(`.ant-tree-node-selected`)

    // console.log(!treeContainer && !selectedNode)
    // 组件启用虚拟化滚动时treeContainer与selectedNode均为null
    if (!(treeContainer && selectedNode)) {
      treeRef.value?.scrollTo({
        key: state.wikiNodeId,
        align: 'top',
        offset: 0,
      })
      return
    }

    // 组件未启用虚拟化滚动时使用以下判断
    if (treeContainer && selectedNode) {
      const containerRect = treeContainer.getBoundingClientRect()
      const nodeRect = selectedNode.getBoundingClientRect()

      // 只有当节点不在可视区域内时才滚动
      const isVisible =
        nodeRect.top >= containerRect.top &&
        nodeRect.bottom <= containerRect.bottom

      // console.log('isVisible:', isVisible)
      if (!isVisible) {
        treeRef.value?.scrollTo({
          key: state.wikiNodeId,
          align: 'top',
          offset: 0,
        })
      }
    }
  }

  // ===== 树组件交互函数 =====

  // a-tree 处理节点展开/收起事件
  const handleExpand = (keys, { expanded, node }) => {
    if (expanded) {
      expandedKeys.value.add(parseInt(node.node_id))
    } else {
      removeKeysRecursively(node)
    }

    // 保存展开状态到 localStorage
    saveExpandedKeys()
  }

  // a-tree 递归移除展开的子节点的 key 取消展开
  const removeKeysRecursively = (node) => {
    const removeKey = (n) => {
      expandedKeys.value.delete(parseInt(n.node_id)) // 删除当前节点 key
      if (n.children) {
        n.children.forEach((child) => removeKey(child)) // 递归删除子节点
      }
    }

    removeKey(node)
  }

  // a-tree 处理拖拽
  const handleDrop = async (info) => {
    let { node, dragNode, dropPosition, dropToGap } = info
    let targetNode = node

    // 等待节点信息刷新
    await new Promise((resolve) => {
      setTimeout(() => {
        resolve()
      }, 100)
    })

    // 获取拖拽节点和目标节点的 id
    const dragNodeId = dragNode.node_id // 拖拽节点
    const targetNodeId = targetNode.node_id // 目标节点

    // 修正 dropPosition 为相对位置值 (-1/0/1)
    const dropPos = info.node.pos.split('-')
    const trueDropPosition = dropPosition - Number(dropPos[dropPos.length - 1])

    try {
      const payload = {
        drag_node_id: dragNodeId,
        target_node: targetNode,
        drop_to_gap: dropToGap,
        drop_position: trueDropPosition, // 使用修正后的位置值
        space_id: currentSpaceId.value,
      }

      // 调用后端接口保存排序结果
      const { msg } = await wikiCatalogRearrangement(payload)
      $baseMessage(msg || '排序成功', 'success', 'vab-hey-message-success')

      // 刷新列表
      await fetchWikiList()
    } catch (error) {
      console.error('拖拽排序失败:', error)
      $baseMessage('排序失败', 'error', 'vab-hey-message-error')
    }
  }

  // ===== 内容编辑函数 =====

  // 编辑Wiki内容
  const handleEditWikiContent = async () => {
    state.inEditMode = true
    state.editable = true

    // 更新编辑器高度
    contentEditorRef.value?.updateScrollbarHeight()
  }

  // 取消编辑
  const handleCancelEdit = async (docId) => {
    // 检查是否存在有效的本地备份
    const backupData = localStorage.getItem(state.backupStorageKey)
    let shouldProceed = true

    if (backupData) {
      try {
        const data = JSON.parse(backupData)
        // 检查备份是否属于当前空间
        if (
          data.spaceId == currentSpaceId.value &&
          data.docId == state.wikiDocId
        ) {
          // 添加确认对话框
          await new Promise((resolve) => {
            $baseConfirm(
              '确定要放弃修改吗？',
              '确认取消编辑',
              () => {
                // 用户确认放弃备份，继续取消编辑
                handleDeleteWikiContentRestore()
                resolve(true)
              },
              () => {
                // 用户不想放弃备份，取消操作
                shouldProceed = false
                resolve(false)
              },
              '确认放弃',
              '继续编辑',
              'warning'
            )
          })
        }
      } catch (e) {
        console.error('解析备份数据失败:', e)
      }
    }

    // 如果用户选择继续编辑，则中断取消操作
    if (!shouldProceed) return

    if (state.wikiInCreating) {
      // 如果是新建状态，则清空内容
      state.mixedText = ''
      state.wikiTitle = ''
      state.wikiDocId = null
      state.wikiInCreating = false
      state.wikiNodeId = null
    }

    state.inEditMode = false
    state.editable = false

    // 恢复原始内容
    if (contentEditorRef.value && !state.wikiInCreating) {
      const originalDoc = state.flattenWikiList[state.wikiDocId]
      if (originalDoc) {
        state.mixedText =
          (state.htmlMode
            ? originalDoc.content_html
            : originalDoc.content_markdown) || ''
        contentEditorRef.value.setEditorValue(state.mixedText)
        state.wikiTitle = originalDoc.title
      }
    }
    await nextTick() // 需要等待内容恢复
    openWikiContentByDocData({ doc_id: parseInt(docId) })

    // 只有在确认放弃备份的情况下才清除localStorage
    if (shouldProceed) {
      handleDeleteWikiContentRestore()
    }
  }

  // 删除Wiki内容
  const deleteWikiContent = async (docId) => {
    await doDelete({
      ids: [docId],
    })
    $baseMessage('删除成功', 'success', 'vab-hey-message-success')
    await fetchWikiList()
    if (route.query.doc_id == docId) {
      pageListModuleClick()
    }
  }

  // 删除备份
  const handleDeleteWikiContentRestore = async () => {
    let backupData = localStorage.getItem(state.backupStorageKey)
    let spaceId = JSON.parse(backupData)?.spaceId
    // console.log(
    //   'handleDeleteWikiContentRestore',
    //   spaceId,
    //   ' and ',
    //   currentSpaceId.value
    // )
    if (spaceId && spaceId == currentSpaceId.value) {
      await new Promise((resolve) => setTimeout(resolve, 700))
      localStorage.removeItem(state.backupStorageKey)
    }
  }

  // 修改saveWikiContent函数
  const saveWikiContent = _.throttle(
    async (content, keepEditorOpen = false) => {
      if (state.wikiTitle == '') {
        $baseMessage('请输入标题', 'warning', 'vab-hey-message-warning')
        return
      }

      try {
        state.saveButtonLoading = true

        // 保存编辑器类型到localStorage
        const editorType = content.htmlMode ? 1 : 2
        localStorage.setItem(state.editorTypeStorageKey, editorType.toString())

        // 使用抽离出的函数处理备份和准备数据
        const payload = await prepareBackupAndPayload(content, editorType)

        // 调用保存API
        const result = await doEdit(payload)

        // console.log('result', result)

        if (result?.code == 200) {
          // console.log('保存成功后清除备份')
          // 保存成功后清除备份
          handleDeleteWikiContentRestore()

          $baseMessage(
            state.wikiInCreating ? '创建成功' : '保存成功',
            'success',
            'vab-hey-message-success'
          )

          // 更新状态
          state.wikiInCreating = false
          if (!keepEditorOpen) {
            state.inEditMode = false
            state.editable = false
          }

          if (result.data && result.data.doc_id) {
            state.wikiDocId = result.data.doc_id
          }

          // 刷新列表
          await fetchWikiList()

          const node = state.flattenWikiList[state.wikiDocId]
          if (node) {
            openWikiContentByDocData(node)
          }
        }
      } catch (error) {
        console.error('保存失败:', error)
        // $baseMessage('保存失败', 'error', 'vab-hey-message-error')

        // 保存失败时保留备份
        $baseMessage(
          '内容已自动备份，请重新登录后继续编辑',
          'warning',
          'vab-hey-message-warning'
        )
      } finally {
        state.saveButtonLoading = false
      }
    },
    1000
  )

  // 备份内容并准备保存数据
  const prepareBackupAndPayload = async (content, editorType) => {
    // 备份内容到localStorage
    const backupData = {
      docId: state.wikiDocId,
      title: state.wikiTitle,
      content: content,
      timestamp: Date.now(),
      spaceId: currentSpaceId.value,
    }
    localStorage.setItem(state.backupStorageKey, JSON.stringify(backupData))

    // 准备保存数据
    const payload = {
      doc_id: state.wikiDocId,
      title: state.wikiTitle,
      content_markdown: content.markdownContent,
      content_html: content.htmlContent,
      space_id: parseInt(currentSpaceId.value),
      current_editor_type: editorType,
    }

    // 如果是新建文档且有父文档ID,添加parent_id
    if (state.wikiInCreating && state.parentDocId) {
      payload.parent_id = state.parentDocId
    }

    return payload
  }

  // 添加恢复备份的函数
  const restoreBackup = async (backupData) => {
    if (backupData) {
      try {
        const data = JSON.parse(backupData)
        // 检查备份是否过期（24小时）
        if (Date.now() - data.timestamp > 24 * 60 * 60 * 1000) {
          handleDeleteWikiContentRestore()
          return
        }

        // 检查备份是否来自当前空间,如果不是则先不恢复
        if (data.spaceId != currentSpaceId.value) {
          return
        }
        // 计算备份时间
        const backupTime = new Date(data.timestamp)
        const formattedTime = `${backupTime.getFullYear()}-${(
          backupTime.getMonth() + 1
        )
          .toString()
          .padStart(2, '0')}-${backupTime
          .getDate()
          .toString()
          .padStart(2, '0')} ${backupTime
          .getHours()
          .toString()
          .padStart(2, '0')}:${backupTime
          .getMinutes()
          .toString()
          .padStart(2, '0')}`

        // 定义一个函数用于显示第一层确认对话框
        const showFirstConfirm = () => {
          $baseConfirm(
            `检测到有未保存的编辑内容 (${formattedTime})，是否恢复？`,
            '恢复未保存内容',
            async () => {
              // 用户确认恢复的逻辑保持不变
              state.wikiDocId = data.docId

              await openWikiContentByDocData({ doc_id: data.docId })
              await nextTick()
              await handleEditWikiContent()

              state.wikiTitle = data.title
              state.mixedText = data.content.htmlMode
                ? data.content.htmlContent
                : data.content.markdownContent
              state.htmlMode = data.content.htmlMode

              // 恢复编辑器内容
              if (contentEditorRef.value) {
                contentEditorRef.value.setEditorValue(state.mixedText)
              }

              $baseMessage(
                '已恢复上次未保存的内容',
                'success',
                'vab-hey-message-success'
              )
              // 恢复成功后清除备份
              handleDeleteWikiContentRestore()
            },
            () => {
              // 用户取消恢复时，添加二次确认
              $baseConfirm(
                '确定要放弃此备份内容吗？放弃后将无法恢复。',
                '确认放弃备份',
                () => {
                  // 用户确认放弃备份
                  handleDeleteWikiContentRestore()
                  $baseMessage('已放弃备份内容', 'info', 'vab-hey-message-info')
                },
                () => {
                  // 用户取消放弃，返回第一层确认
                  showFirstConfirm()
                },
                '确认放弃',
                '返回选择',
                'warning'
              )
            },
            '恢复',
            '取消'
          )
        }

        // 初始显示第一层确认
        showFirstConfirm()
      } catch (e) {
        console.error('恢复备份失败:', e)
      }
    }
  }

  // 添加处理标签页关闭的函数
  const handleBeforeUnload = (event) => {
    // 检查是否有未保存的内容
    if (contentEditorRef.value?.checkUnsavedContent()) {
      // 使用 confirm 确认是否关闭
      const confirmClose = window.confirm('检测到内容未保存，确定要离开吗？')

      if (!confirmClose) {
        // 如果用户选择不关闭，阻止默认行为
        event.preventDefault()
        event.returnValue = ''
        return false
      }
    }
  }

  // ===== 事件处理函数 =====

  // 显示用户信息
  const showUserInfo = (userInfo) => {
    // $baseMessage(`用户: ${userInfo.name}`, 'info', 'vab-hey-message-info')
  }

  // 处理图片点击
  const handleImageClick = ({ srcList, imgIndex }) => {
    state.showImage = true
    state.imgIndex = imgIndex
    state.srcList = srcList
  }

  // 关闭图片查看器
  const closeViewer = () => {
    state.showImage = false
  }

  // 上传成功处理
  const handleUploadSuccess = async (value) => {
    $baseMessage('上传成功', 'success', 'vab-hey-message-success')
    // 实现上传成功后的逻辑
  }

  // 内容变化处理
  const wikiContentChange = _.debounce(async (receivedContent) => {
    console.log('wikiContentChange')
    // 重新构造符合要求的 content 对象结构
    // 获取编辑器中的最新内容
    const formattedContent = {
      htmlContent: contentEditorRef.value?.getHtmlValue() || '',
      markdownContent: contentEditorRef.value?.getMarkdownValue() || '',
      htmlMode: state.htmlMode,
    }

    // 计算编辑器类型
    const editorType = state.htmlMode ? 1 : 2

    // 调用备份函数
    prepareBackupAndPayload(formattedContent, editorType)
  }, 300)

  // Wiki标题变更
  const wikiTitleChanged = async (title) => {
    state.wikiTitle = title
  }

  // 设置Wiki内容
  const setWikiContent = async (data) => {
    const [content, shouldRecover] = data

    if (shouldRecover) {
      state.mixedText = content

      // 更新编辑器内容
      if (contentEditorRef.value) {
        contentEditorRef.value.setEditorValue(content)
      }
    } else {
      // 如果不恢复，通常是退出历史记录查看
      state.showWikiHistory = false
      state.el_col_right_lg_history = 0

      // 重新获取当前文档
      await fetchWikiList()
    }
  }

  // ===== 数据获取函数 =====

  // 获取Wiki列表
  const fetchWikiList = async (customQuery) => {
    await getCurrentMemberRole()

    state.slowLoad1 = true
    try {
      const query = customQuery || state.queryForm
      const res = await getWikiCatalogList(query)

      // 转换为树形结构
      const items = res.data.flattenedData || []
      const result = res.data.data

      state.data = res.data.data

      // 更新平铺列表
      state.flattenWikiList = {}
      state.flattenWikiListTitleAsKey = {}

      items.forEach((item) => {
        state.flattenWikiList[item.doc_id] = item
        state.flattenWikiListTitleAsKey[item.title] = item
      })

      emit('update:data', result)
    } catch (error) {
      console.error('获取Wiki列表失败:', error)
      $baseMessage('获取列表失败', 'error', 'vab-hey-message-error')
    } finally {
      state.slowLoad1 = false
    }
  }

  // 页面列表模块点击
  const pageListModuleClick = () => {
    if (contentEditorRef.value?.checkUnsavedContent()) {
      window.alert('检测到内容未保存')
      return
    }
    activePage.value = 'space'

    // 构建查询参数，始终包含标签页状态
    const query = {
      space_id: currentSpaceId.value,
      tab: spaceTabActiveName.value, // 始终包含tab参数
    }

    router.push({
      path: '/Oa/Wiki/Detail',
      query,
    })
  }

  // 键盘事件监听 - Ctrl+S 保存
  const handleKeyDown = (event) => {
    if (event.ctrlKey && (event.key === 's' || event.key === 'S')) {
      event.preventDefault() // 阻止浏览器默认保存行为

      if (state.inEditMode) {
        const content = {
          markdownContent: contentEditorRef.value?.getMarkdownValue() || '',
          htmlContent: contentEditorRef.value?.getHtmlValue() || '',
          htmlMode: state.htmlMode,
        }
        saveWikiContent(content, true)
      }
    }
  }
  /**
   * 处理文档刷新请求
   * @param {number} docId - 需要刷新的文档ID
   */
  const handleRefreshDocument = async (docId) => {
    if (docId) {
      await fetchWikiList()
      openWikiContentByDocData({ doc_id: parseInt(docId) })
    }
  }

  // 添加打开目录设置对话框的函数
  const openCatalogSettings = (data, newChildCatalogPid) => {
    if (!hasEditPermission.value) {
      $baseMessage('无权限', 'warning', 'vab-hey-message-warning')
      return
    }

    catalogSettingsDialogRef.value?.showSettings(
      data,
      currentSpaceId.value,
      newChildCatalogPid
    )
  }

  // 打开项目Wiki导入对话框
  const openProjectWikiImport = (data) => {
    if (!hasEditPermission.value) {
      $baseMessage('无权限', 'warning', 'vab-hey-message-warning')
      return
    }

    projectWikiImportDialogRef.value?.showImport(data)
  }

  // 导出目录为zip
  const exportCatalog = async (data) => {
    if (!hasEditPermission.value) {
      $baseMessage('无权限', 'warning', 'vab-hey-message-warning')
      return
    }
    // 检查是否有任何目录正在导出
    if (isAnyExporting.value) {
      $baseMessage(
        '正在处理导出请求，请稍候...',
        'warning',
        'vab-hey-message-warning'
      )
      return
    }

    try {
      // 设置全局导出状态为导出中
      isAnyExporting.value = true

      // 根据目录下的文档数量给出等待提示
      const docCount = data.doc_count || 0
      let waitMsg = '正在准备导出...'

      if (docCount > 50) {
        waitMsg = '文档较多，可能需要等待较长时间，请耐心等待...'
      } else if (docCount > 20) {
        waitMsg = '文档数量较多，请耐心等待...'
      }

      $baseMessage(waitMsg, 'info', 'vab-hey-message-info')

      // 禁用菜单选项
      await new Promise((resolve) => setTimeout(resolve, 100)) // 确保UI更新

      const res = await wikiExportCatalogRecursive({
        catalog_id: data.catalog_id,
      })

      const blob = new Blob([res], { type: 'application/zip' })
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = data.title + '.zip'
      a.click()
      window.URL.revokeObjectURL(url)

      $baseMessage('导出成功', 'success', 'vab-hey-message-success')
    } catch (e) {
      console.error('导出失败', e)
      $baseMessage('导出失败', 'error', 'vab-hey-message-error')
    } finally {
      // 无论成功或失败，都重置全局导出状态
      isAnyExporting.value = false
    }
  }

  // 打开标签管理
  const openTagManager = () => {
    state.tagDialogVisible = true

    // 等待对话框打开后刷新标签列表
    setTimeout(() => {
      tagTableRef.value?.fetchTagList()
    }, 100)
  }

  // 关闭标签管理时刷新标签列表
  const handleTagDialogClose = () => {
    // 如果需要刷新文档标签列表
    if (state.currentDoc?.doc_id) {
      fetchDocumentTags(state.currentDoc.doc_id)
    }
  }

  // 添加保存展开状态的函数
  const saveExpandedKeys = () => {
    try {
      // 获取现有的存储数据
      const storageData = JSON.parse(
        localStorage.getItem(state.expandedKeysStorageKey) || '{}'
      )

      // 更新当前 space_id 的展开状态
      storageData[currentSpaceId.value] = Array.from(expandedKeys.value)

      // 保存回 localStorage
      localStorage.setItem(
        state.expandedKeysStorageKey,
        JSON.stringify(storageData)
      )
    } catch (error) {
      console.error('保存展开状态失败:', error)
    }
  }

  // 添加恢复展开状态的函数
  const restoreExpandedKeys = () => {
    try {
      const storageData = JSON.parse(
        localStorage.getItem(state.expandedKeysStorageKey) || '{}'
      )
      const savedKeys = storageData[currentSpaceId.value]

      if (savedKeys && Array.isArray(savedKeys)) {
        // 恢复保存的展开状态
        expandedKeys.value = new Set(savedKeys)
      }
    } catch (error) {
      console.error('恢复展开状态失败:', error)
    }
  }

  // 初始化标签页状态，从URL查询参数中读取
  const initializeTabState = () => {
    const tabFromUrl = route.query.tab
    if (tabFromUrl && ['recentUpdate', 'allDocuments'].includes(tabFromUrl)) {
      spaceTabActiveName.value = tabFromUrl
    } else {
      // 如果URL中没有tab参数，设置默认值并更新URL
      spaceTabActiveName.value = 'recentUpdate'
      const currentQuery = { ...route.query }
      currentQuery.tab = 'recentUpdate'
      router.push({
        path: route.path,
        query: currentQuery,
      })
    }
  }

  // 监听标签页变化，同步到URL
  watch(spaceTabActiveName, (newTab) => {
    if (activePage.value === 'space' && !isRouteSyncing.value) {
      const currentQuery = { ...route.query }
      // 所有标签页都需要明确的tab参数以支持浏览器历史记录
      currentQuery.tab = newTab

      // 只有当URL中的tab参数与当前标签页不同时才进行路由跳转
      if (route.query.tab !== newTab) {
        router.push({
          path: route.path,
          query: currentQuery,
        })
      }
    }
  })

  // ===== 监听与生命周期 =====

  watch(
    () => activePage.value,
    async (newVal) => {
      await nextTick()
      if (
        newVal &&
        treeRef?.value &&
        typeof treeRef.value.setCurrentKey === 'function'
      ) {
        treeRef.value.setCurrentKey(null)
      }
    }
  )

  // 监听路由变化，同步标签页状态和数据
  watch(
    () => route.query.tab,
    (newTab, oldTab) => {
      // 处理tab参数变化的所有情况
      if (newTab !== oldTab) {
        isRouteSyncing.value = true

        if (newTab && ['recentUpdate', 'allDocuments'].includes(newTab)) {
          // 有效的tab参数
          spaceTabActiveName.value = newTab
          // 如果当前在文档页面且有tab参数，说明需要切换到空间页面
          if (route.query.doc_id && activePage.value !== 'space') {
            activePage.value = 'space'
          }
        } else if (!newTab && oldTab) {
          // tab参数被移除，设置为默认值
          spaceTabActiveName.value = 'recentUpdate'
          // 如果当前有doc_id但没有tab参数，保持在文档页面
          if (!route.query.doc_id && activePage.value !== 'space') {
            activePage.value = 'space'
          }
        }

        // 重置同步标志
        nextTick(() => {
          isRouteSyncing.value = false
        })
      }
    }
  )

  // 监听文档ID变化，处理浏览器前进/后退导航
  watch(
    () => route.query.doc_id,
    async (newDocId, oldDocId) => {
      if (newDocId && newDocId !== oldDocId) {
        // 文档ID发生变化，打开对应文档
        await nextTick()
        openWikiContentByDocData({ doc_id: parseInt(newDocId) })
      } else if (!newDocId && oldDocId) {
        // 从文档页面返回到空间页面
        activePage.value = 'space'
      }
    }
  )

  // 监听整个路由查询参数变化，处理复杂的导航情况
  watch(
    () => ({ doc_id: route.query.doc_id, tab: route.query.tab }),
    (newQuery, oldQuery) => {
      const { doc_id: newDocId, tab: newTab } = newQuery
      const { doc_id: oldDocId, tab: oldTab } = oldQuery

      // 如果doc_id和tab都没有变化，不需要处理
      if (newDocId === oldDocId && newTab === oldTab) {
        return
      }

      console.log('Route query changed:', { newQuery, oldQuery })

      // 处理从文档页面到空间页面的导航
      if (
        !newDocId &&
        oldDocId &&
        newTab &&
        ['recentUpdate', 'allDocuments'].includes(newTab)
      ) {
        // 从文档页面导航到空间页面的特定标签页
        isRouteSyncing.value = true
        activePage.value = 'space'
        spaceTabActiveName.value = newTab
        nextTick(() => {
          isRouteSyncing.value = false
        })
      }
    },
    { deep: true }
  )

  // 暴露方法
  defineExpose({
    fetchWikiList,
    openWikiContentByDocData,
  })

  // 定义一个变量来存储移除路由守卫的函数
  let removeRouterGuard = null

  // 在 onMounted 中
  onMounted(async () => {
    // 初始化树组件高度
    calculateTreeHeight()

    // 初始化标签页状态
    initializeTabState()

    // 初始化依赖于 currentSpaceId 的值
    state.queryForm.filter.space_id = currentSpaceId.value
    state.backupStorageKey = 'wiki_content_backup' + currentSpaceId.value

    await fetchWikiList()
    restoreExpandedKeys() // 恢复展开状态
    window.addEventListener('keydown', handleKeyDown)
    window.addEventListener('resize', handleResize)

    // 获取备份数据
    const backupData = localStorage.getItem(state.backupStorageKey)

    if (route.query.doc_id) {
      // 如果URL中有doc_id参数
      if (backupData) {
        // 优先尝试恢复备份数据
        restoreBackup(backupData)
      } else {
        // 没有备份数据则直接打开文档
        openWikiContentByDocData({ doc_id: route.query.doc_id })
      }
    } else {
      // 没有指定文档ID时，等待树结构加载完成
      await new Promise((resolve) => setTimeout(resolve, 1250))
      // 尝试恢复可能存在的备份内容
      if (backupData) {
        restoreBackup(backupData)
      }
    }

    // 添加标签页关闭事件监听
    window.addEventListener('beforeunload', handleBeforeUnload)

    // 添加全局路由守卫并保存移除函数的引用
    removeRouterGuard = router.beforeEach((to, from, next) => {
      // console.log('state.wikiInCreating', state.wikiInCreating)
      // 检查是否有未保存的内容且处于编辑模式
      if (
        !state.wikiInCreating &&
        state.editable &&
        state.inEditMode &&
        contentEditorRef.value?.checkUnsavedContent()
      ) {
        // 弹出确认对话框
        window.alert('检测到内容未保存')
        const confirmLeave = window.confirm('确定要离开吗？')
        if (confirmLeave) {
          next() // 用户确认离开
        } else {
          next(false) // 取消导航
        }
      } else {
        next() // 没有未保存内容，正常导航
      }
    })

    // 订阅置顶相关事件
    $sub('wiki-document-pin-success', () => {
      fetchWikiList()
    })

    $sub('wiki-document-unpin-success', () => {
      fetchWikiList()
    })
  })

  // 只保留一个 onBeforeUnmount 钩子函数，确保在组件顶层定义
  onBeforeUnmount(() => {
    saveExpandedKeys() // 保存展开状态
    window.removeEventListener('keydown', handleKeyDown)
    window.removeEventListener('resize', handleResize)

    // 移除标签页关闭事件监听
    window.removeEventListener('beforeunload', handleBeforeUnload)

    // 移除路由守卫（如果存在）
    if (removeRouterGuard) {
      removeRouterGuard()
    }

    $unsub('wiki-document-pin-success')
    $unsub('wiki-document-unpin-success')
  })
</script>

<style lang="scss" scoped>
  :deep() {
    .el-tabs__nav-wrap::after {
      height: 1px;
    }
    .ant-tree {
      .ant-tree-treenode {
        margin-bottom: 0; // itemHeight没有计算margin-bottom, 会导致树结构高度计算错误底下部分item无法滚动触及将不可视
      }
    }
    .el-tabs__header {
      margin: 0px;
    }
    .el-card__body {
      padding: 0 0px 0 0px !important;
    }
    .ant-tree-switcher {
      padding-top: 7px;
    }

    .ant-tree-list-scrollbar {
      // right: 2px !important;
      width: 6px !important;
    }
    .ant-tree-list-scrollbar-thumb {
      background-color: rgba(144, 147, 153, 0.3) !important;
      border-radius: 4px !important;
    }
    .ant-tree-list-scrollbar {
      opacity: 0 !important;
      transition: opacity 0.3s;
    }
    .ant-tree-list-holder {
      overflow-x: clip;
    }
    .ant-tree-list:hover .ant-tree-list-scrollbar,
    .ant-tree-list.scrolling .ant-tree-list-scrollbar {
      opacity: 1 !important;
    }
    .ant-tree .ant-tree-node-content-wrapper {
      line-height: 32px;
    }
    .ant-tree-node-content-wrapper:hover .bi-fixed-right-icon {
      visibility: visible;
    }
    .ant-tree-node-content-wrapper:hover .ant-tree-list-scrollbar {
      opacity: 1 !important;
    }

    // a-tree 节点样式定制 - 修改整个节点区域
    .ant-tree-treenode {
      // margin-bottom: 6px;
      border-radius: 6px;
      transition: background-color 0.3s;

      // 整个节点的悬停效果
      &:hover {
        background-color: #ebf1fe !important;

        .bi-fixed-right-icon {
          visibility: visible;
        }

        // 确保在节点悬停时内容区域不要有自己的背景色
        .ant-tree-node-content-wrapper {
          background-color: transparent !important;
        }
      }

      // 节点内容区域清除默认背景样式
      .ant-tree-node-content-wrapper {
        line-height: 32px;
        background: transparent !important;
        transition: none;

        &:hover {
          background-color: transparent !important;
        }
      }

      // 选中节点时整个节点的样式
      &.ant-tree-treenode-selected,
      &:has(.ant-tree-node-selected) {
        background-color: #ebf1fe !important;
        border-radius: 6px;

        .ant-tree-node-selected {
          background-color: transparent !important;
        }
      }
    }

    // 无编辑权限时禁用hover效果
    .no-edit-permission {
      // 保持hover时图标显示，但禁用点击功能由模板层控制
      .ant-tree-node-content-wrapper:hover .bi-fixed-right-icon {
        visibility: visible;
      }

      .ant-tree-treenode {
        &:hover {
          // 保持正常的hover背景色
          background-color: #ebf1fe !important;

          .bi-fixed-right-icon {
            visibility: visible;
          }

          .ant-tree-node-content-wrapper {
            background-color: transparent !important;
          }
        }
      }
    }

    .ant-tree-treenode-selected {
      background-color: #ebf1fe !important;
      border-radius: 6px;
    }

    // 调整树节点内部元素与整个节点的间距
    .ant-tree-list-holder-inner {
      padding: 0 6px;
    }

    // 确保 hover 时显示滚动条
    .ant-tree-list:hover .ant-tree-list-scrollbar,
    .ant-tree-list.scrolling .ant-tree-list-scrollbar {
      opacity: 1 !important;
    }
  }

  .bi-wiki-tree-list {
    width: 100%;
    height: calc(100vh - 172px);
    // padding-right: 10px;

    // 无编辑权限时保持图标显示
    &.no-edit-permission {
      .ant-tree-node-content-wrapper:hover .bi-fixed-right-icon {
        visibility: visible;
      }
    }
  }

  .bi-tree-node-content {
    max-width: calc(100% - 75px);
    overflow-x: clip;
    overflow-y: clip;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .bi-fixed-right-icon {
    position: absolute;
    right: 1%;
    display: flex;
    align-items: center;
    visibility: visible;
    transition: all 0.3s ease;
  }

  .wiki-content-doc-count-container {
    margin-right: 4px;
    transition: transform 0.3s ease;
  }

  .ant-tree-node-content-wrapper:hover .wiki-content-doc-count-container {
    transform: translateX(-4px);
  }

  // 无编辑权限时禁用全局hover效果
  .no-edit-permission {
    .ant-tree-node-content-wrapper:hover .wiki-content-doc-count-container {
      transform: none;
    }

    .ant-tree-treenode:hover .wiki-content-doc-count {
      transform: none;
    }
  }

  .wiki-content-doc-count {
    position: relative;
    z-index: 0;
    margin-top: 2px;
    font-size: 14px;
    color: #999;
    border-radius: 10px;
    transition: transform 0.3s ease;
  }

  /* 确保悬浮时图标正常显示 */
  :deep(.ant-tree-treenode:hover) .bi-hoverable-item-wiki-option-icon {
    visibility: visible;
    opacity: 1;
  }

  /* 无编辑权限时保持悬浮图标显示 */
  .no-edit-permission
    :deep(.ant-tree-treenode:hover)
    .bi-hoverable-item-wiki-option-icon {
    visibility: visible;
    opacity: 1;
  }

  .bi-hoverable-item-wiki-option-container {
    position: absolute;
    right: 0;
    z-index: 1;
    margin-top: 4px;
  }

  .bi-hoverable-item-wiki-option-icon {
    color: #999999;
    visibility: hidden;
    opacity: 0;
    transition: visibility 0.3s, color 0.3s, opacity 0.3s;

    &:hover {
      color: $base-color-primary;
    }
  }

  .bi-hoverable-item-wiki {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 8px 10px;
    font-size: 15px;
    cursor: pointer;
    background-color: white;
    border-radius: 6px;
    transition: background-color 0.3s;

    &:hover {
      color: $base-color-primary;
      background-color: #f0f0f0;
    }

    &.color-danger:hover {
      color: #f56c6c;
    }
  }

  .bi-wiki-navigation {
    padding-bottom: 4px;
    margin: 0px 8px 8px 0;
    border-bottom: 1px solid #dcdfe6;
  }

  .bi-wiki-nav-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 4px;
    font-size: 14px;
    cursor: pointer;
    border-radius: 4px;

    &:hover {
      background-color: rgba(57, 119, 243, 0.1);
    }

    &.active {
      font-weight: 500;
      color: #3977f3;
      background-color: rgba(57, 119, 243, 0.1);
    }

    .el-icon {
      margin-right: 8px;
    }
  }

  .wiki-content-el-popover-self {
    width: 138px !important;
    min-width: 138px !important;
  }

  /* 添加动画效果 */
  .custom-switcher-icon {
    display: inline-block;
    transition: transform 0.2s ease; /* 动画延迟为 200ms */
  }

  /* 确保对话框中的按钮和表格元素样式正确 */
  .el-dialog .el-button,
  .el-dialog .el-table *,
  .el-dialog .el-pagination * {
    box-sizing: border-box !important;
  }

  /* 添加悬浮时数字左移效果 */
  .ant-tree-treenode:hover .wiki-content-doc-count {
    transform: translateX(-24px);
  }

  /* 文档权限弹窗样式 */
  .wiki-document-privacy-dialog {
    .wiki-document-privacy-form {
      .el-form-item {
        margin-bottom: 0;
      }

      .el-radio-group {
        display: flex;
        gap: 32px;
        width: 100%;
      }

      .el-radio {
        margin-right: 0;
        padding: 8px 0;
      }
    }
  }

  /* 新建菜单样式 */
  .wiki-create-menu {
    padding: 8px 0;

    .wiki-create-menu-item {
      display: flex;
      align-items: center;
      padding: 8px 12px;
      font-size: 14px;
      cursor: pointer;
      border-radius: 4px;
      transition: background-color 0.3s;

      &:hover {
        background-color: rgba(57, 119, 243, 0.1);
      }

      .el-icon {
        margin-right: 8px;
      }
    }
  }

  /* 置顶图标样式 */
  .wiki-content-pin-icon {
    cursor: pointer;
    margin-right: 2px;
    margin-left: -6px;
  }
</style>
