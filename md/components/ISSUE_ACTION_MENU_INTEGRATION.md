# IssueActionMenu 组件集成总结

## 完成的工作

### 1. 创建了独立的 IssueActionMenu 组件
- 文件位置: `src/components/IssueActionMenu.vue`
- 功能: 提供复制标题&链接、复制链接、事项复制、删除事项等操作
- 支持 props: `rowData`, `isVisible`, `isSelected`
- 支持 events: `popover-show`, `popover-hide`, `copy-title`, `copy-link`, `copy-issue`, `delete-issue`

### 2. 在 IssueChildListTable.vue 中集成组件
- 导入组件: `import IssueActionMenu from '@/components/IssueActionMenu.vue'`
- 在操作列中添加组件使用
- 添加状态管理: `visiblePopoverIds` 数组
- 添加事件处理函数:
  - `handlePopoverShow`
  - `handlePopoverHide`
  - `handleCopyTitle`
  - `handleCopyLink`
  - `handleCopyIssue`
  - `handleDeleteIssue`

### 3. 样式处理
- 创建全局样式文件: `src/styles/issue-action-menu.scss`
- 在 `src/app.scss` 中导入全局样式
- 移除组件内部重复的样式定义
- 确保表格行 hover 时显示操作图标的样式正确应用

## 组件使用方式

```vue
<IssueActionMenu
  :row-data="row"
  :is-visible="state.visiblePopoverIds.includes(row.id)"
  :is-selected="false"
  @popover-show="handlePopoverShow"
  @popover-hide="handlePopoverHide"
  @copy-title="handleCopyTitle"
  @copy-link="handleCopyLink"
  @copy-issue="handleCopyIssue"
  @delete-issue="handleDeleteIssue"
/>
```

## 样式特性

1. **操作图标默认隐藏**: `opacity: 0`
2. **Hover 显示**: 表格行 hover 时显示操作图标
3. **强制显示**: 当 `isVisible` 或 `isSelected` 为 true 时强制显示
4. **交互效果**: 
   - 图标 hover 时变色
   - 菜单项 hover 时背景变色
   - 删除项使用红色文字

## 功能说明

1. **复制标题&链接**: 复制格式为 "#ID 标题\n链接"
2. **复制链接**: 复制事项详情页面链接
3. **事项复制**: 跳转到事项复制页面
4. **删除事项**: 显示确认对话框后删除

## 测试文件

创建了测试页面: `src/views/test/IssueActionMenuTest.vue`
可以用于验证组件功能是否正常。

## 注意事项

1. 确保传入的 `rowData` 包含必要字段: `id`, `project_id`, `subject`
2. 复制功能依赖浏览器的 Clipboard API
3. 删除功能会调用父组件的删除方法
4. 样式已全局化，避免在多个地方重复定义
