# IssueActionMenu 组件

## 概述

`IssueActionMenu` 是一个可复用的事项操作菜单组件，提供了复制标题&链接、复制链接、事项复制和删除等常用操作。

## 功能特性

- 🔗 复制事项标题和链接
- 📋 复制事项链接
- 📄 事项复制（跳转到复制页面）
- 🗑️ 删除事项（带确认对话框）
- 🎨 支持显示状态控制
- 📱 响应式设计

## Props

| 属性名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| rowData | Object | 是 | {} | 事项行数据，需包含 id、project_id、subject 等字段 |
| isVisible | Boolean | 否 | false | 是否强制显示操作图标 |
| isSelected | Boolean | 否 | false | 行是否被选中 |

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| popover-show | rowId: number | popover 显示时触发 |
| popover-hide | rowId: number | popover 隐藏时触发 |
| copy-title | rowData: object | 复制标题&链接后触发 |
| copy-link | rowData: object | 复制链接后触发 |
| copy-issue | rowData: object | 事项复制后触发 |
| delete-issue | rowData: object | 删除事项时触发（用户确认后） |

## 使用示例

### 基本使用

```vue
<template>
  <IssueActionMenu
    :row-data="issueData"
    :is-visible="isMenuVisible"
    :is-selected="isRowSelected"
    @popover-show="handlePopoverShow"
    @popover-hide="handlePopoverHide"
    @copy-title="handleCopyTitle"
    @copy-link="handleCopyLink"
    @copy-issue="handleCopyIssue"
    @delete-issue="handleDeleteIssue"
  />
</template>

<script setup>
import IssueActionMenu from '@/components/IssueActionMenu.vue'

const issueData = {
  id: 123,
  project_id: 456,
  subject: '示例事项标题'
}

const handlePopoverShow = (rowId) => {
  console.log('显示操作菜单:', rowId)
}

const handlePopoverHide = (rowId) => {
  console.log('隐藏操作菜单:', rowId)
}

const handleCopyTitle = (row) => {
  console.log('复制标题:', row.subject)
}

const handleCopyLink = (row) => {
  console.log('复制链接:', row.id)
}

const handleCopyIssue = (row) => {
  console.log('复制事项:', row.id)
}

const handleDeleteIssue = (row) => {
  // 执行删除逻辑
  console.log('删除事项:', row.id)
}
</script>
```

### 在表格中使用

```vue
<template>
  <el-table :data="tableData">
    <el-table-column label="操作" width="200">
      <template #default="{ row }">
        <div style="display: flex; gap: 8px; align-items: center">
          <!-- 其他操作按钮 -->
          <el-button size="small">编辑</el-button>
          
          <!-- 操作菜单 -->
          <IssueActionMenu
            :row-data="row"
            :is-visible="visiblePopoverIds.includes(row.id)"
            :is-selected="selectedRows.some(item => item.id === row.id)"
            @popover-show="handlePopoverShow"
            @popover-hide="handlePopoverHide"
            @delete-issue="handleDeleteIssue"
          />
        </div>
      </template>
    </el-table-column>
  </el-table>
</template>
```

## 样式说明

组件包含以下样式特性：

- 操作图标默认透明，hover 时显示
- 当 `isVisible` 或 `isSelected` 为 true 时强制显示
- popover 内容项支持 hover 效果
- 删除项使用红色文字

## 依赖

- Element Plus (el-popover)
- Vue Router (用于页面跳转)
- 项目内的 vab-icon 组件

## 注意事项

1. 确保传入的 `rowData` 包含必要的字段：`id`、`project_id`、`subject`
2. 复制功能依赖浏览器的 Clipboard API
3. 删除操作会显示确认对话框，需要用户确认
4. 事项复制功能会跳转到 `/project/issueCopy` 页面
