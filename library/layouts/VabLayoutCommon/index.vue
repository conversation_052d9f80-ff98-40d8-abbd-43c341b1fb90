<!--常规布局 -->
<script lang="ts" setup>
  import MenuBar from '@/components/MenuBar.vue'
  import { VabRoute } from '~/types/router'
  const route: VabRoute = useRoute()
  defineProps({
    collapse: {
      type: <PERSON>olean,
      default() {
        return false
      },
    },
    fixedHeader: {
      type: <PERSON><PERSON><PERSON>,
      default() {
        return true
      },
    },
    showTabs: {
      type: <PERSON>olean,
      default() {
        return true
      },
    },
    device: {
      type: String,
      default() {
        return 'desktop'
      },
    },
  })
</script>

<template>
  <div class="vab-layout-common">
    <div class="content-box">
      <div>
        <firefly-column-bar />
      </div>
      <div class="vab-main">
        <MenuBar v-if="!route.meta.isCustomHead">
          <template #prefix>{{ route.meta.title }}</template>
        </MenuBar>
        <vab-app-main />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .content-box {
    display: flex;
  }
  .vab-layout-common {
    :deep() {
      .vab-tabs-content {
        width: calc(
          100% - 60px - #{$base-font-size-default} - #{$base-padding} - 2px
        ) !important;
      }
    }
  }
  .vab-main {
    height: 100vh;
    // height: calc(100vh - 80px);
    overflow: auto;
  }
</style>
