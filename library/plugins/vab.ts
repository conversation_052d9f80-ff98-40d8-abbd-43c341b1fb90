import { App } from 'vue'
import { loadingText, messageDuration } from '@/config'
import {
  ElLoading,
  ElMessage,
  ElMessageBox,
  ElNotification,
} from 'element-plus'
import mitt from 'mitt'
import _ from 'lodash'
import { globalPropertiesType } from '/#/library'
import { fa } from 'element-plus/es/locale'

export let gp: globalPropertiesType

export default {
  install(app: App<Element>) {
    gp = {
      /**
       * @description 全局加载层
       * @param {number} index 自定义加载图标类名ID
       * @param {string} text 显示在加载图标下方的加载文案
       */
      $baseLoading: (index = undefined, text = loadingText) => {
        return ElLoading.service({
          lock: true,
          text,
          spinner: index ? `vab-loading-type${index}` : index,
          background: 'hsla(0,0%,100%,.8)',
        })
      },
      /**
       * @description 全局Message
       * @param {string} message 消息文字
       * @param {'success'|'warning'|'info'|'error'} type 主题
       * @param {string} customClass 自定义类名
       * @param {boolean} dangerouslyUseHTMLString 是否将message属性作为HTML片段处理
       */
      $baseMessage: (
        message,
        type = 'info',
        customClass,
        dangerouslyUseHTMLString
      ) => {
        ElMessage({
          message,
          type,
          customClass,
          duration: messageDuration,
          dangerouslyUseHTMLString,
          showClose: true,
          grouping: true,
        })
      },
      /**
       * @description 全局Alert
       * @param {string|VNode} content 消息正文内容
       * @param {string} title 标题
       * @param {function} callback 若不使用Promise,可以使用此参数指定MessageBox关闭后的回调
       */
      $baseAlert: (content, title = '温馨提示', callback = undefined) => {
        if (title && typeof title == 'function') {
          callback = title
          title = '温馨提示'
        }
        ElMessageBox.alert(content, title, {
          confirmButtonText: '确定',
          dangerouslyUseHTMLString: true, // 此处可能引起跨站攻击，建议配置为false
          callback: () => {
            if (callback) callback()
          },
        }).then(() => {})
      },
      /**
       * @description 全局Confirm
       * @param {string|VNode} content 消息正文内容
       * @param {string} title 标题
       * @param {function} callback1 确认回调
       * @param {function} callback2 关闭或取消回调
       * @param {string} confirmButtonText 确定按钮的文本内容
       * @param {string} cancelButtonText 取消按钮的自定义类名
       */
      $baseConfirm: (
        content,
        title,
        callback1,
        callback2,
        confirmButtonText = '确认',
        cancelButtonText = '取消',
        showCancelButton = true,
        showClose = false,
        type: '',
        center = false
      ) => {
        ElMessageBox.confirm(content, title || '温馨提示', {
          confirmButtonText,
          cancelButtonText,
          autofocus: false, // 避免按钮被自动focus
          closeOnClickModal: false,
          lockScroll: false,
          showClose: showClose, // 控制是否显示 X 按钮
          type: type, // 控制title图标 'success' | 'info' | 'warning' | 'error' | ''
          center: center,
          // customClass: 'custom-confirm',
          showCancelButton: showCancelButton,
          beforeClose: (action, instance, done) => {
            if (action === 'cancel') {
              if (
                document.activeElement &&
                document.activeElement.classList.contains(
                  'el-message-box__headerbtn'
                )
              ) {
                // 点击 X 按钮时
                if (callback2) {
                  callback2('close') // 触发 X 按钮专用的回调
                }
              } else {
                // 点击取消按钮时
                if (callback2) {
                  callback2('cancel') // 触发取消操作的回调
                }
              }
              done() // 必须调用 done 关闭对话框
            } else {
              done() // 处理 confirm 情况
            }
          },
        })
          .then(() => {
            if (callback1) {
              callback1() // 确认按钮的回调
            }
          })
          .catch(() => {
            // 这里不再执行 callback2，因为已经在 beforeClose 中处理
          })
      },
      /**
       * @description 全局Prompt封装
       * @param {string|VNode} content 消息正文内容
       * @param {string} title 标题
       * @param {function} callback1 确认回调
       * @param {function} callback2 取消或关闭回调
       * @param {string} confirmButtonText 确认按钮文本内容
       * @param {string} cancelButtonText 取消按钮文本内容
       * @param {string} inputPlaceholder 输入框占位符
       * @param {boolean} showClose 是否显示关闭按钮
       * @param {string} type 弹窗类型 'success' | 'info' | 'warning' | 'error' | ''
       * @param {boolean} center 内容是否居中
       */
      $basePrompt: (
        content,
        title,
        callback1,
        callback2,
        confirmButtonText = '确认',
        cancelButtonText = '取消',
        inputPlaceholder = '请输入内容',
        showClose = false,
        type = '',
        center = false,
        inputValidator = null, // 可选的输入校验函数
        useHtml = false // 是否使用HTML内容
      ) => {
        ElMessageBox.prompt(content, title || '温馨提示', {
          confirmButtonText,
          cancelButtonText,
          autofocus: false, // 避免按钮被自动focus
          closeOnClickModal: false,
          lockScroll: false,
          showClose: showClose, // 是否显示 X 按钮
          type: type, // 控制标题图标 'success' | 'info' | 'warning' | 'error' | ''
          center: center, // 是否居中显示
          inputPlaceholder, // 输入框占位符
          inputValidator, // 自定义输入校验
          dangerouslyUseHTMLString: useHtml, // 使用HTML内容时必须开启
          beforeClose: (action, instance, done) => {
            if (action === 'cancel') {
              if (
                document.activeElement &&
                document.activeElement.classList.contains(
                  'el-message-box__headerbtn'
                )
              ) {
                if (callback2) {
                  callback2('close') // 触发 X 按钮关闭的回调
                }
              } else {
                if (callback2) {
                  callback2('cancel') // 触发取消操作的回调
                }
              }
              done()
            } else {
              done()
            }
          },
        })
          .then(({ value }) => {
            if (callback1) {
              callback1(value) // 确认后的回调
            }
          })
          .catch(() => {
            // 取消或关闭的回调已在 beforeClose 中处理
          })
      },

      /**
       * @description 全局Notification
       * @param {string} message 说明文字
       * @param {string} title 标题
       * @param {'success'|'warning'|'info'|'error'} type 主题样式,如果不在可选值内将被忽略
       * @param {'top-right'|'top-left'|'bottom-right'|'bottom-left'} position 自定义弹出位置
       * @param duration 显示时间,毫秒
       */
      $baseNotify: (
        message,
        title,
        type = 'success',
        position = 'top-right',
        duration = messageDuration
      ) => {
        ElNotification({
          title,
          message,
          type,
          duration,
          position,
        })
      },
      /**
       * @description 表格高度
       * @param {*} formType
       */
      $baseTableHeight: (formType) => {
        let height = window.innerHeight
        const paddingHeight = 291
        const formHeight = 60

        if ('number' === typeof formType) {
          height = height - paddingHeight - formHeight * formType
        } else {
          height = height - paddingHeight
        }
        return height
      },
      $pub: (...args: any[]) => {
        _emitter.emit(_.head(args), args[1])
      },
      $sub: function () {
        // eslint-disable-next-line prefer-rest-params
        Reflect.apply(_emitter.on, _emitter, _.toArray(arguments))
      },
      $unsub: function () {
        // eslint-disable-next-line prefer-rest-params
        Reflect.apply(_emitter.off, _emitter, _.toArray(arguments))
      },
    }

    const _emitter = mitt()
    Object.keys(gp).forEach((key) => {
      app.provide(key, gp[key as keyof typeof gp])
      // 允许vue3下继续使用vue2中的this调用vab方法
      app.config.globalProperties[key] = gp[key as keyof typeof gp]
    })

    if (process.env['NODE_' + 'ENV'] !== `${'deve' + 'lopme' + 'nt'}`) {
      const key = 'vab-' + 'icons'
      if (!__APP_INFO__['dependencies'][key]) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        app.config.globalProperties = null
      }
      if (!process.env['VUE_' + 'APP_' + 'SECRET_' + 'KEY']) {
        // eslint-disable-next-line @typescript-eslint/ban-ts-comment
        // @ts-ignore
        app.config.globalProperties = null
      }
    }
  },
}
