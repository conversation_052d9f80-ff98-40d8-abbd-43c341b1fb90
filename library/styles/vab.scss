/**
 * @description 全局样式
 */
@import 'element-plus/theme-chalk/display.css';
@import '@arco-design/web-vue/dist/arco.css';
@import './normalize.scss';
@import './transition.scss';
// @import './dialog-animation.scss';

@mixin base-scrollbar {
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: mix($base-color-white, $base-menu-background, 90%);
    border: 3px solid transparent;
    border-radius: 7px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: mix($base-color-white, $base-menu-background, 80%);
  }
}

.vab-layout-header,
[class*='-bar-container'] {
  transition: $base-transition;

  * {
    transition: $base-transition;
  }
}

html {

  body,
  body[class*='vab-theme-'] {
    position: relative;
    box-sizing: border-box;
    height: 100vh;
    padding: 0;
    overflow: hidden;
    font-family: 'PingFang SC', <PERSON><PERSON>, 'Microsoft YaHei', sans-serif;
    font-size: $base-font-size-default;
    color: var(--el-color-black);
    background: $base-color-background;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    #app {
      height: 100vh;
      overflow: auto;
      @include base-scrollbar;

      .vab-main {
        transition: $base-transition;

        .vab-app-main {
          width: 100%;
          padding: $base-padding;
          overflow: hidden;
          transition: $base-transition;

          >section {
            background: var(--el-color-white);
            transition: $base-transition;

            >[class*='-container'] {
              min-height: $base-keep-alive-height;
              padding: $base-padding;
              background: var(--el-color-white);
              transition: $base-transition;
            }
          }
        }
      }
    }

    * {
      box-sizing: border-box;
      outline: none !important;
      @include base-scrollbar;
    }

    [class*='ri-'] {
      vertical-align: -3px !important;
    }

    .vab-icon {
      margin: 0 3px 0 0;
    }

    /*a标签 */
    a {
      // color: var(--el-color-primary);
      color: $base-color-text-primary;
      text-decoration: none;
    }

    /*图片 */
    img {
      object-fit: cover;

      &[src=''],
      &:not([src]) {
        opacity: 0;
      }
    }

    /* vab-dropdown下拉动画 */
    .vab-dropdown {
      transition: $base-transition;

      &-active {
        transform: rotateZ(180deg);
      }
    }

    /* vab-dot圆点动画 */
    .vab-dot {
      position: relative;
      display: inline-block;
      width: 6px;
      height: 6px;
      margin-right: 3px;
      vertical-align: middle;
      border-radius: 50%;

      span {
        position: absolute;
        top: 0;
        left: 0;
        box-sizing: border-box;
        display: block;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        animation: vabDot 1.2s ease-in-out infinite;

        @keyframes vabDot {
          0% {
            opacity: 0.6;
            transform: scale(0.8);
          }

          to {
            opacity: 0;
            transform: scale(2.4);
          }
        }
      }

      &-success {
        background: var(--el-color-success);

        span {
          background: var(--el-color-success);
        }
      }

      &-error {
        background: var(--el-color-error);

        span {
          background: var(--el-color-error);
        }
      }
    }

    /* vab-data-empty占位图 */
    .vab-data-empty {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 600px;
      margin: auto;
    }

    /* el-descriptions */
    .el-descriptions {
      &__title {
        padding-left: 10px;
        border-left: 5px solid var(--el-color-primary);
      }
    }

    /* el-button按钮 */
    // .el-button {
    //   border-radius: var(--el-border-radius-base);

    //   &--primary {
    //     --el-button-bg-color: var(--el-color-primary) !important;
    //     --el-button-border-color: var(--el-color-primary) !important;
    //     --el-button-hover-bg-color: var(--el-color-primary-light-1) !important;
    //     --el-button-hover-border-color: var(
    //       --el-color-primary-light-1
    //     ) !important;
    //     --el-button-active-bg-color: var(--el-color-primary) !important;
    //     --el-button-active-border-color: var(--el-color-primary) !important;

    //     &.is-disabled {
    //       --el-button-bg-color: var(--el-color-primary) !important;
    //       --el-button-border-color: var(--el-color-primary) !important;
    //       --el-button-hover-bg-color: var(
    //         --el-color-primary-light-1
    //       ) !important;
    //       --el-button-hover-border-color: var(
    //         --el-color-primary-light-1
    //       ) !important;
    //       --el-button-active-bg-color: var(--el-color-primary) !important;
    //       --el-button-active-border-color: var(--el-color-primary) !important;
    //       --el-button-disabled-bg-color: var(
    //         --el-color-primary-light-4
    //       ) !important;
    //       --el-button-disabled-border-color: var(
    //         --el-color-primary-light-4
    //       ) !important;

    //       &.is-plain {
    //         color: var(--el-color-primary-light-5) !important;
    //         background-color: var(--el-color-primary-light-9) !important;
    //         border-color: var(--el-color-primary-light-8) !important;
    //       }
    //     }

    //     &.is-plain {
    //       --el-button-bg-color: var(--el-color-primary-light-9) !important;
    //       --el-button-border-color: var(--el-color-primary-light-7) !important;
    //       --el-button-text-color: var(--el-color-primary) !important;
    //       --el-button-hover-text-color: var(--el-color-white);
    //       --el-button-hover-bg-color: var(--el-color-primary) !important;
    //       --el-button-hover-border-color: var(--el-color-primary) !important;
    //       --el-button-active-bg-color: var(--el-color-primary) !important;
    //       --el-button-active-text-color: var(--el-color-white);
    //       --el-button-active-border-color: var(--el-color-primary) !important;
    //     }
    //   }

    //   &.is-round {
    //     border-radius: var(--el-border-radius-round);
    //   }

    //   &.is-circle {
    //     border-radius: var(--el-border-radius-circle);
    //   }

    //   [class*='ri-'] {
    //     margin-right: 3px;
    //     font-size: 12px;
    //     vertical-align: -1.8px;
    //   }

    //   .el-icon {
    //     font-size: 12px;
    //   }

    //   [class*='el-icon-'] + span {
    //     margin-left: 3px;
    //   }
    // }

    /* el-tag */
    .el-tag {
      border-radius: var(--el-border-radius-base);

      &+.el-tag {
        margin-left: 10px;
      }

      &--light:not(&--success, &--info, &--warning, &--danger) {
        --el-tag-bg-color: var(--el-color-primary-light-9);
        --el-tag-border-color: var(--el-color-primary-light-8);
        --el-tag-text-color: var(--el-color-primary);
        --el-tag-hover-color: var(--el-color-primary);
      }

      &--dark:not(&--success, &--info, &--warning, &--danger) {
        --el-tag-bg-color: var(--el-color-primary);
        --el-tag-border-color: var(--el-color-primary);
        --el-tag-hover-color: var(--el-color-primary-2);
      }

      &.is-round {
        border-radius: var(--el-border-radius-round);
      }
    }

    /* .el-select-tags */
    .el-select-tags-wrapper {
      .el-tag.el-tag {
        margin-left: 0px;
      }
    }

    a+a,
    /*   span + span, */
    a+.el-button,
    .el-button+a {
      margin-left: 10px;
    }

    .el-drawer__wrapper {
      outline: none !important;

      * {
        outline: none !important;
      }
    }

    /* el-overlay遮罩 */
    .el-overlay {
      // background-color: rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(3px);
    }

    /* el-image-viewer遮罩 */
    .el-image-viewer__mask {
      // background-color: rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(3px);
    }

    /* v-modal遮罩 */
    .v-modal {
      z-index: $base-z-index;
      background-color: rgba(0, 0, 0, 0.5);
      opacity: 0.6;
      //backdrop-filter: blur(10px);
    }

    /* el-loading-mask遮罩 */
    .el-loading-mask {
      z-index: $base-z-index - 10 !important;

      &.is-fullscreen {
        z-index: $base-z-index + 99 !important;
      }
    }

    /* el-scrollbar滚动条 */
    .el-scrollbar {
      height: 100%;

      &__bar {
        z-index: 999;
      }

      &__thumb {
        background-color: mix($base-color-white, $base-menu-background, 90%);

        &:hover {
          background-color: mix($base-color-white, $base-menu-background, 80%);
        }
      }
    }

    /* el-form表单 */
    .el-form--label-top {
      .el-form-item__label {
        padding: 0;
      }
    }

    .el-form-item__label {
      padding: 0 10px 0 0;
    }

    .el-range-editor--small {

      .el-range__icon,
      .el-range__close-icon {
        line-height: 23.5px;
      }
    }

    /*  el-badge */
    .el-badge__content {
      border: 0;
    }

    /*  .el-page-header */
    .el-page-header {
      margin: 0 0 $base-margin 0;
    }

    /* el-alert */
    .el-alert {
      margin: 0 0 $base-margin 0;

      &__closebtn {
        position: absolute !important;
      }

      &--success.is-light {
        color: var(--el-color-success);
        background-color: var(--el-color-success-lighter);
        border: 1px solid var(--el-color-success);

        i {
          color: var(--el-color-success);
        }
      }

      &--info.is-light {
        color: var(--el-color-primary);
        background-color: var(--el-color-primary-light-9);
        border: 1px solid var(--el-color-primary);

        i {
          color: var(--el-color-primary);
        }
      }

      &--warning.is-light {
        color: var(--el-color-warning);
        background-color: var(--el-color-warning-lighter);
        border: 1px solid var(--el-color-warning);

        i {
          color: var(--el-color-warning);
        }
      }

      &--error.is-light {
        color: var(--el-color-error);
        background-color: var(--el-color-error-lighter);
        border: 1px solid var(--el-color-error);

        i {
          color: var(--el-color-error);
        }
      }
    }

    /* el-divider间隔线 */
    .el-divider--horizontal {
      margin: 8px 0 $base-margin + 8px 0;

      .el-divider__text {
        display: -webkit-box;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }
    }

    /* nprogress进度条 */
    #nprogress {
      position: fixed;
      z-index: $base-z-index + 3;

      .bar {
        background: var(--el-color-primary);
      }

      .peg {
        box-shadow: 0 0 10px var(--el-color-primary),
          0 0 5px var(--el-color-primary);
      }
    }

    /* el-table表格 */
    .el-table {
      .el-table__body-wrapper {
        @include base-scrollbar;
      }

      th {
        background: #f5f7fa !important;
      }

      td,
      th {
        position: relative;
        box-sizing: border-box;

        .cell {
          font-size: $base-font-size-default;
          font-weight: normal;
          // color: #606266;
          color: $base-color-text-primary;

          .el-image {
            width: 50px;
            height: 50px;
            border-radius: $base-border-radius;
          }
        }
      }
    }

    /* el-pagination分页 */
    .el-pagination {
      justify-content: center;
      margin: $base-margin 0 0 0;
      font-weight: normal;
      color: var(--el-color-black);
    }

    /* el-menu菜单开始 */
    .el-menu,
    .vab-column-grid {
      user-select: none;

      /* plus处理图标间距 */
      div,
      li,
      span {
        i+span {
          margin-left: 3px;
        }
      }

      &.vab-column-grid-card,
      &.vab-column-grid-vertical {
        div {
          i+span {
            margin-left: 0;
          }
        }
      }
    }

    /* el-dialog、el-message-box、el-popover、el-button、el-tag */
    @media (max-width: 576px) {

      .el-dialog,
      .el-message-box,
      .el-popover.el-popper {
        width: auto !important;
        margin: 5vw;
      }

      .el-button {
        margin-bottom: 10px;
      }
    }

    /* el-card卡片 */
    .el-card {
      margin-bottom: $base-margin;
      border-radius: var(--el-border-radius-base);

      &__header {
        position: relative;

        .card-header-tag {
          position: absolute;
          top: 15px;
          right: 20px;
        }
      }

      &__body {
        padding: $base-padding;
      }
    }

    /* .vab-hey-message */
    .vab-hey-message {
      @mixin vab-hey-message {
        min-width: 246px;
        padding: 15px;
        background-color: var(--el-color-white);
        border-color: var(--el-color-white);
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15);

        .el-message__content {
          padding-right: $base-padding;
          color: #34495e;
        }

        .el-icon-close {
          color: #34495e;

          &:hover {
            opacity: 0.8;
          }
        }
      }

      &-info {
        @include vab-hey-message;

        i {
          color: $base-color-grey;
        }
      }

      &-success {
        @include vab-hey-message;

        i {
          color: var(--el-color-success);
        }
      }

      &-warning {
        @include vab-hey-message;

        i {
          color: var(--el-color-warning);
        }
      }

      &-error {
        @include vab-hey-message;

        i {
          color: var(--el-color-error);
        }
      }
    }

    /* vab-table-expand */
    .vab-table-expand {
      padding: $base-padding;
      line-height: 30px;

      &-title {
        display: inline-block;
        width: 80px;
        font-weight: bold;
      }
    }
  }
}