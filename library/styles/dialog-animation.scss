/* 参考 dialog Effects 的 sally css效果实现的 */
.el-dialog__wrapper {
	transition-duration: 0.3s;
}
.dialog-fade-enter-active{
	animation: none !important;
}
.dialog-fade-leave-active {
	transition-duration: 0.15s !important;
	animation: none !important;
  }

.dialog-fade-enter-active .el-dialog,
.dialog-fade-leave-active .el-dialog{
	animation-fill-mode: forwards;
}

.dialog-fade-enter-active {
  .el-dialog{
    animation-name: anim-open;
    animation-duration: 0.3s;
    animation-timing-function: cubic-bezier(0.6,0,0.4,1);
  }
}

.dialog-fade-leave-active .el-dialog{
	animation-name: anim-close;
	animation-duration: 0.3s;
}


@keyframes anim-open {
	0% { opacity: 0;  transform: scale3d(0, 0, 1); }
	100% { opacity: 1; transform: scale3d(1, 1, 1); }
}


@keyframes anim-close {
	0% { opacity: 1; }
	100% { opacity: 0; transform: scale3d(0.5, 0.5, 1); }
}