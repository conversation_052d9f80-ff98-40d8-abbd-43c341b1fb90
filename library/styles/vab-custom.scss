/**
 * @description 全局样式
 */
//  @import 'element-plus/theme-chalk/display.css';
@import './normalize.scss';
@import './transition.scss';
// @import './hljs.css'; // 通用高亮样式
// @import './dialog-animation.scss';

@font-face {
  font-family: 'HuaweiSans';
  font-style: normal;
  font-weight: normal;
  src: url('./fonts/HarmonyOS_Sans_SC_Regular.woff');
  font-display: swap;
  /* 先显示备用字体，等鸿蒙字体加载完再替换 */
}

@mixin base-scrollbar {
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(51, 51, 51, 0.30);
    border: 3px solid transparent;
    border-radius: 10.5px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: rgba(51, 51, 51, 0.40);
  }

  &::-webkit-scrollbar-thumb:active {
    background: rgba(51, 51, 51, 0.50);
  }


}

.vab-layout-header,
[class*='-bar-container'] {
  transition: $base-transition;

  * {
    transition: $base-transition;
  }
}

html {


  body,
  body[class*='vab-theme-'] {
    // vxe-table部分样式定制
    --vxe-ui-font-color: #{$base-color-text-desc};
    --vxe-ui-table-header-font-weight: 400;
    --vxe-ui-form-title-background-color: #{$base-background-color-base};
    --vxe-ui-table-header-background-color: #{$base-background-color-base};
    --vxe-ui-list-design-table-header-background-color:#{$base-background-color-base};
    --vxe-ui-table-border-radius: #{$base-border-radius};
    --vxe-ui-table-row-height-default: #{$base-table-row-height-45};
    --vxe-ui-table-column-padding-default: 6px 0;
    --vxe-ui-table-border-width: 0px;



    //直接引用scss变量是无效的
    --el-color-primary: #{$base-color-primary};
    --el-color-success: #{$base-color-success};

    --el-color-warning: #{$base-color-warning};
    --el-color-danger: #{$base-color-danger};

    --el-text-color-regula: #666; //form label 文字颜色
    --el-border-radius-base: 6px;

    position: relative;
    box-sizing: border-box;
    height: 100vh;
    padding: 0;
    overflow: hidden;
    font-family: 'HuaweiSans', 'PingFang SC', Arial, 'Microsoft YaHei', sans-serif;
    font-size: $base-font-size-default;
    color: var(--el-color-black);
    // background: $base-color-background;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;

    .el-input,
    .el-date-editor--daterange,
    .el-picker-panel__body-wrapper {
      --el-input-border-radius: #{$base-border-radius};
    }

    #app {
      height: 100vh;
      overflow: auto;
      @include base-scrollbar;

      .vab-main {
        transition: $base-transition;

        .vab-app-main {
          width: 100%;
          overflow: hidden;
          transition: $base-transition;

          >section {
            background: var(--el-color-white);
            // border-radius: $base-border-radius;
            transition: $base-transition;

            >[class*='-container'] {
              min-height: $base-keep-alive-height;
              padding: $base-padding;
              background: var(--el-color-white);
              border-radius: $base-border-radius;
              transition: $base-transition;
            }
          }
        }
      }
    }

    * {
      box-sizing: border-box;
      outline: none !important;
      @include base-scrollbar;
    }

    [class*='ri-'] {
      vertical-align: -3px !important;
    }

    .vab-icon {
      margin: 0 3px 0 0;
    }

    /*a标签 */
    a {
      // color: var(--el-color-primary);
      color: $base-color-text-primary;
      text-decoration: none;
    }

    /*图片 */
    img {
      object-fit: cover;

      &[src=''],
      &:not([src]) {
        opacity: 0;
      }
    }

    /* vab-dropdown下拉动画 */
    .vab-dropdown {
      transition: $base-transition;

      &-active {
        transform: rotateZ(180deg);
      }
    }

    /* vab-dot圆点动画 */
    .vab-dot {
      position: relative;
      display: inline-block;
      width: 6px;
      height: 6px;
      margin-right: 3px;
      vertical-align: middle;
      border-radius: 50%;

      &.large {
        width: 12px;
        height: 12px;
      }

      span {
        position: absolute;
        top: 0;
        left: 0;
        box-sizing: border-box;
        display: block;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        animation: vabDot 1.5s ease-in-out infinite;

        @keyframes vabDot {
          0% {
            opacity: 0.8;
            transform: scale(0.8);
          }

          to {
            opacity: 0;
            transform: scale(2.4);
          }
        }
      }

      &-grey {
        background: #c6d4e8;

        span {
          background: #c6d4e8;
        }
      }

      &-success {
        background: var(--el-color-success);

        span {
          background: var(--el-color-success);
        }
      }

      &-error {
        background: var(--el-color-error);

        span {
          background: var(--el-color-error);
        }
      }
    }

    /* vab-data-empty占位图 */
    .vab-data-empty {
      display: flex;
      align-items: center;
      justify-content: center;
      min-height: 600px;
      margin: auto;
    }

    /* el-descriptions */
    .el-descriptions {
      &__title {
        padding-left: 10px;
        border-left: 5px solid var(--el-color-primary);
      }
    }

    /* el-button按钮 */
    .el-button {
      // height: auto;//统一高度
      // padding: 10px 18px;
      border-radius: 6px;

      // [class*='ri-'] {
      //   margin-right: 5px;
      //   font-size: 14px;
      //   vertical-align: -1.8px;
      // }
    }

    /* el-tag */
    .el-tag {

      // height: auto;
      // padding: 7px 15px;
      // border: none;
      // border-radius: 6px;
      .el-tag__content {
        // font-size: 14px;
        font-weight: 500;
      }

      &--success {
        background: #e5fcf4;
      }

      &--primary {
        .el-tag__content {
          color: $base-color-primary;
        }

        background: #e6f3ff;
      }

      &--warning {
        background: #fff1d6;
      }

      &+.el-tag {
        margin-left: 10px;
      }

      &--light:not(&--success, &--info, &--warning, &--danger) {
        --el-tag-bg-color: var(--el-color-primary-light-9);
        --el-tag-border-color: var(--el-color-primary-light-8);
        --el-tag-text-color: var(--el-color-primary);
        --el-tag-hover-color: var(--el-color-primary);
      }

      &--dark:not(&--success, &--info, &--warning, &--danger) {
        --el-tag-bg-color: var(--el-color-primary);
        --el-tag-border-color: var(--el-color-primary);
        --el-tag-hover-color: var(--el-color-primary-2);
      }

      &.is-round {
        border-radius: var(--el-border-radius-round);
      }
    }

    /* .el-select-tags */
    .el-select-tags-wrapper {
      .el-tag.el-tag {
        margin-left: 0px;
      }
    }

    a+a,
    /*   span + span, */
    a+.el-button,
    .el-button+a {
      margin-left: 10px;
    }

    .el-drawer__wrapper {
      outline: none !important;

      * {
        outline: none !important;
      }
    }

    /* el-overlay遮罩 */
    .el-overlay {
      // background-color: rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(3px);
    }

    /* el-image-viewer遮罩 */
    .el-image-viewer__mask {
      // background-color: rgba(0, 0, 0, 0.1);
      backdrop-filter: blur(3px);
    }

    /* el-tooltip样式 */
    .el-popper.is-dark {
      max-width: 360px !important;
    }

    /* v-modal遮罩 */
    .v-modal {
      z-index: $base-z-index;
      background-color: rgba(0, 0, 0, 0.5);
      opacity: 0.6;
      //backdrop-filter: blur(10px);
    }

    /* el-loading-mask遮罩 */
    .el-loading-mask {
      z-index: $base-z-index - 10 !important;

      &.is-fullscreen {
        z-index: $base-z-index + 99 !important;
      }
    }

    /* el-scrollbar滚动条 */
    .el-scrollbar {

      &__bar {
        z-index: 999;
      }

      // &__thumb {
      //   background-color: mix($base-color-white, $base-menu-background, 90%);

      //   &:hover {
      //     background-color: mix($base-color-white, $base-menu-background, 80%);
      //   }
      // }
    }

    /* el-form表单 */
    .el-form--label-top {
      .el-form-item__label {
        padding: 0;
      }
    }

    .el-form-item__label {
      padding: 0 10px 0 0;
    }

    .el-range-editor--small {

      .el-range__icon,
      .el-range__close-icon {
        line-height: 23.5px;
      }
    }

    /*  el-badge */
    .el-badge__content {
      border: 0;
    }

    /*  .el-page-header */
    .el-page-header {
      margin: 0 0 $base-margin 0;
    }

    /* el-alert */
    .el-alert {
      margin: 0 0 $base-margin 0;

      &__closebtn {
        position: absolute !important;
      }

      &--success.is-light {
        color: var(--el-color-success);
        background-color: var(--el-color-success-lighter);
        border: 1px solid var(--el-color-success);

        i {
          color: var(--el-color-success);
        }
      }

      &--info.is-light {
        color: var(--el-color-primary);
        background-color: var(--el-color-primary-light-9);
        border: 1px solid var(--el-color-primary);

        i {
          color: var(--el-color-primary);
        }
      }

      &--warning.is-light {
        color: var(--el-color-warning);
        background-color: var(--el-color-warning-lighter);
        border: 1px solid var(--el-color-warning);

        i {
          color: var(--el-color-warning);
        }
      }

      &--error.is-light {
        color: var(--el-color-error);
        background-color: var(--el-color-error-lighter);
        border: 1px solid var(--el-color-error);

        i {
          color: var(--el-color-error);
        }
      }
    }

    /* el-divider间隔线 */
    .el-divider--horizontal {
      margin: 8px 0 $base-margin + 8px 0;
      border-top: 1px #DCDFE6 solid;

      .el-divider__text {
        display: -webkit-box;
        overflow: hidden;
        text-overflow: ellipsis;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
      }
    }

    /* nprogress进度条 */
    #nprogress {
      position: fixed;
      z-index: $base-z-index + 3;

      .bar {
        background: var(--el-color-primary);
      }

      .peg {
        box-shadow: 0 0 10px var(--el-color-primary),
          0 0 5px var(--el-color-primary);
      }
    }

    /* el-table表格 */
    .el-table {
      .el-table__body-wrapper {
        @include base-scrollbar;
      }

      th {
        background: #f5f7fa !important;
      }

      td,
      th {
        position: relative;
        box-sizing: border-box;

        .cell {
          font-size: $base-font-size-default;
          font-weight: normal;
          // color: #606266;

          .el-image {
            width: 50px;
            height: 50px;
            border-radius: $base-border-radius;
          }
        }
      }

      td {
        .cell {
          color: $base-color-text-primary;
        }
      }

      th {
        .cell {
          color: $base-color-text-title;
        }
      }
    }

    // el-message-box关闭按钮图标位置
    .el-message-box {
      .el-message-box__header {
        .el-message-box__headerbtn {
          // margin-top: -19px !important; typecenter // center 属性不为true时 x关闭符号作用在content行需要调整x符号位置
          // margin-right: -14px !important;
          font-size: 21px !important;
        }
      }

    }

    // center属性为true才能使图标作用在title行，但不需要各种居中
    .el-message-box--center {
      .el-message-box__content {
        text-align: left !important;
      }

      .el-message-box__header .el-message-box__title {
        justify-content: flex-start !important;
      }

      .el-message-box__btns {
        justify-content: end !important;
      }

      .el-message-box__message {
        white-space: pre-line;
      }

      .el-message-box__message p {
        line-height: 16px;
      }
    }


    /* el-pagination分页 */
    .el-pagination {
      justify-content: flex-start;
      margin: $base-margin 0 0 0;
      font-weight: normal;
      color: var(--el-color-black);
    }

    /* el-menu菜单开始 */
    .el-menu,
    .vab-column-grid {
      user-select: none;

      /* plus处理图标间距 */
      div,
      li,
      span {
        i+span {
          margin-left: 3px;
        }
      }

      &.vab-column-grid-card,
      &.vab-column-grid-vertical {
        div {
          i+span {
            margin-left: 0;
          }
        }
      }
    }

    /* el-dialog、el-message-box、el-popover、el-button、el-tag */
    @media (max-width: 576px) {

      .el-dialog,
      .el-message-box,
      .el-popover.el-popper {
        width: auto !important;
        margin: 5vw;
      }

      .el-button {
        margin-bottom: 10px;
      }
    }

    /* el-card卡片 */
    .el-card {
      margin-bottom: $base-margin;
      border-radius: var(--el-border-radius-base);

      &__header {
        position: relative;

        .card-header-tag {
          position: absolute;
          top: 15px;
          right: 20px;
        }
      }

      &__body {
        padding: $base-padding;
      }
    }

    /* .vab-hey-message */
    .vab-hey-message {
      @mixin vab-hey-message {
        min-width: 246px;
        padding: 15px;
        background-color: var(--el-color-white);
        border-color: var(--el-color-white);
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15);

        .el-message__content {
          padding-right: $base-padding;
          color: #34495e;
        }

        .el-icon-close {
          color: #34495e;

          &:hover {
            opacity: 0.8;
          }
        }
      }

      &-info {
        @include vab-hey-message;

        i {
          color: $base-color-grey;
        }
      }

      &-success {
        @include vab-hey-message;

        i {
          color: var(--el-color-success);
        }
      }

      &-warning {
        @include vab-hey-message;

        i {
          color: var(--el-color-warning);
        }
      }

      &-error {
        @include vab-hey-message;

        i {
          color: var(--el-color-error);
        }
      }
    }

    /* vab-table-expand */
    .vab-table-expand {
      padding: $base-padding;
      line-height: 30px;

      &-title {
        display: inline-block;
        width: 80px;
        font-weight: bold;
      }
    }
  }
}

.color-red {
  color: red !important;
}

.color-text-secondary {
  color: $base-color-text-secondary;
}

//统一定义了pre标签样式
pre {
  // 原来的样式
  // display: flex;
  // align-items: center;
  // padding: 8px;
  // background-color: #f5f2f0;
  // border: 1px solid #e2e2e2;
  // border-radius: 3px;

  // 兼容vditor后的样式
  background-color: #f5f2f0;
  border-radius: 3px;

  code {
    margin: 0px !important;
    border: none !important;
  }
}

.flex {
  display: flex !important;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-flex-end {
  display: flex !important;
  justify-content: flex-end;
}


.justify-content-end {
  display: flex !important;
  justify-content: flex-end;
}

.justify-content-space-between {
  justify-content: space-between;
}

.align-items-center {
  display: flex !important;
  align-items: center;
}

.align-items-flex-start {
  align-items: flex-start;
}

.text-color-secondary {
  color: $base-color-text-secondary;
}

.cursor-pointer {
  cursor: pointer;
}

// .el-popover.el-popper {
//   max-height: 500px;
//   overflow-y: auto;
// }
// .el-popover.el-popper {
//   padding: 4px !important;
// }
.table-column-more {
  display: flex;
  flex-direction: column;
}

.table-column-more-item {
  height: 36px;
  padding-left: 8px;
  font-size: $base-font-size-default;
  line-height: 36px;
  color: $base-color-text-primary;
  cursor: pointer;
  border-radius: 4px;
}

.table-column-more-item:hover {
  background-color: $base-background-color-base;
}

.margin-left-2 {
  margin-left: 2px;
}

.margin-left-4 {
  margin-left: 4px;
}

.margin-left-6 {
  margin-left: 6px;
}

.margin-left-8 {
  margin-left: 8px;
}

.margin-left-10 {
  margin-left: 10px;
}

.margin-top-2 {
  margin-top: 2px;
}

.margin-top-4 {
  margin-top: 4px;
}

.margin-top-6 {
  margin-top: 6px;
}

.margin-top-8 {
  margin-top: 8px;
}

.margin-top-10 {
  margin-top: 10px;
}

.color-red {
  color: red !important;
}

.rounded-full {
  border-radius: 9999px;
}

.flex-align-center {
  display: flex;
  align-items: center;
}

.flex-align-justify-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

//主区域padding
.pd-container {
  padding: 12px;
}

.pd-container-version-2 {
  padding: 12px;
  padding-left: 16px;
  margin-top: 6px;
}

.forms-container {
  height: 100%;
  padding: 20px;
  padding-bottom: 0px;
}



.custom-card {
  padding: 12px; //默认16，如特殊的再单独设置
  border: 1px solid #DCDFE6;
  border-radius: 10px;
}

.firefly-dialog {
  border-radius: 10px !important;
}

.color-primary {
  color: $base-color-primary !important
}

.color-success {
  color: $base-color-success !important
}

.color-warning {
  color: $base-color-warning !important
}

.color-danger {
  color: $base-color-danger !important
}

.background-primary {
  background: $base-color-primary !important
}

.background-success {
  background: $base-color-success !important
}

.background-warning {
  background: $base-color-warning !important
}

.background-danger {
  background: $base-color-danger !important
}

.no-ellipsis .cell {
  text-overflow: clip !important; //有些表格列无需去显示...
}

.common-link {
  cursor: pointer;

  &:hover {
    color: $base-color-primary;
    text-decoration: underline;
  }
}

.el-table--group,
.el-table {
  border-radius: 6px !important;
}

//统一圆点
.inline-dot {
  font-size: 12px;
  line-height: 14px;
  color: #999;
}

//组件里设置了为16px，更改需important
.common-icon {
  width: 20px !important;
  height: 20px !important;
}


// .none-border-on-common {
//   border: none;

//   &:hover {
//     bottom: initial;
//   }
// }

.none-border-on-common {
  .el-input__wrapper {
    box-shadow: 0 0 0 0px var(--el-input-border-color, var(--el-border-color)) inset;

    .el-input__suffix {
      visibility: hidden;
    }

    &:hover {
      .el-input__suffix {
        visibility: visible;
      }

      box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset;
      // padding-left: 12px;
    }

    &:focus {
      box-shadow: 0 0 0 1px var(--el-select-input-focus-border-color) inset !important;
    }
  }

  .el-input__wrapper.is-focus {
    box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset;
  }

  .el-select__wrapper {
    border-radius: 6px !important;
    box-shadow: 0 0 0 0px var(--el-border-color) inset;

    .el-select__suffix {
      visibility: hidden;
    }

    &:hover {
      .el-select__suffix {
        visibility: visible;
      }

      box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset;
      // padding-left: 12px;
    }

    &:focus {
      box-shadow: 0 0 0 1px var(--el-select-input-focus-border-color) inset !important;
    }
  }

}

//选择器禁用过的样式
.no-background-disabled-selection {
  .el-select__wrapper {
    background-color: var(--el-input-bg-color, var(--el-fill-color-blank)) !important;
    box-shadow: 0 0 0 0px var(--el-input-border-color, var(--el-border-color)) inset !important;
  }
}

.is-disabled {
  .el-input__inner {
    -webkit-text-fill-color: #333 !important;
  }

  .el-input__wrapper {

    //下拉箭头
    .el-input__suffix {
      visibility: hidden !important;
    }

    &:hover {
      .el-input__suffix {
        visibility: visible;
      }

      box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset !important;
      // padding-left: 12px;
    }

    &:focus {
      box-shadow: 0 0 0 1px var(--el-select-input-focus-border-color) inset !important;
    }


  }
}

.aie-container {
  .el-input__wrapper.is-focus {
    box-shadow: 0 0 0 1px var(--el-input-focus-border-color) inset;
  }

  .mention {
    color: $base-color-primary !important;
  }
}

/* 文字自动省略号*/
.white-space-nowrap {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 按钮统一间距 */
.el-button+.el-button {
  margin-left: 8px !important;
}

/* 关闭按钮统一样式 <el-button text class="close-btn"><CommonIcon :type="'close'" /></el-button> */
.close-btn {
  width: 32px;
  height: 32px;
  padding: 0px;
}

.custom-hoverable-select-item {
  display: flex;
  align-items: center;
  padding: 8px 10px;
  font-size: 15px;
  cursor: pointer;

  /* 初始背景颜色 */
  background-color: white;
  border-radius: 6px;
  transition: background-color 0.3s;

  &:hover {
    color: $base-color-primary;
    background-color: #f0f0f0;
  }

  &.active {
    color: #3977f7;
    /* 点击后的字体颜色 */
  }

  .check-icon {
    margin-left: auto;
  }
}

.custom-firefly-dialog-header-title {
  font-size: 16px;
  font-weight: bold;
  line-height: 1em;
  color: #333;
}