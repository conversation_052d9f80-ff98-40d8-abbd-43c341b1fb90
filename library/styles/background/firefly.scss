/**
 * @description 自定义样式
 */

body.vab-theme-custom {
  $base-color-blue: #3977F3;
  $base-color-blue-active: #399efd;

  @mixin container {
    background: $base-color-blue;
  }

  .logo-container-horizontal {
    background: var(--el-color-primary) !important;
  }

  .logo-container-vertical,
  .logo-container-comprehensive,
  .logo-container-float {
    @include container;
  }

  .logo-container-column {
    .logo {
      @include container;
    }
  }

  .vab-column-bar-container {

    &-card {
      .el-tabs {
        .el-tabs__item {
          &.is-active {
            background: transparent !important;
          }
        }
      }
    }
  }


  .vab-header {
    background-color: $base-color-blue !important;
  }


  //以下均为自定义样式
  .vab-side-bar{
    width: 112px;
  }

}

///element 自己内部定义el-popper类里的样式，需要取消掉
.el-popper .custom-sub-menu{
  height: auto !important;
  line-height: 40px;
  background: none !important;
  

  .el-sub-menu__title{
    background: none !important;
    &.is-active,&:hover{
      color:  #3977F3;
    }
    
  }
  
}


///自定义菜单样式
.custom-sub-menu {
  background: none;
  border-radius: 6px;
  &:hover {
    background: none !important;
  }
  .title{
    height: 40px; line-height: 40px
  }

  .el-sub-menu__title {
    height: 100% !important;
    padding: 0px 0px 0px 13px !important;
    background: none;
    &:hover {
      color: #3977f3 !important;
    }
  }

  .el-menu-item {
    & > span {
      padding-left: 27px;
    }
  }

  &.is-opened,
  &:hover {
    .el-sub-menu__title {
      color: #3977f3 !important;
    }
    i{
      color: #3977f3 !important;
    }
  }
}

///自定义菜单具体项样式
.custom-menu-item {
  padding: 0px !important;
  margin: 2px 0;
  border-radius: 6px;
  & > span {
    height: 100%;
    padding-left: 15px;
    // line-height: 40px;
  }

  &.is-active{
    color:#3977F3 !important;
    background-color: #EBF1FE !important;
  }
  &:hover{
    color:#333 !important;
    background-color: #EBF1FE !important;
  }
}
///自定义的confirm 样式
.custom-confirm{
  padding: 24px;
  border-radius: 10px;
  .el-message-box__header{
    padding: 0px;
  }
  
  .el-message-box__title{
    font-size: 16px;
    font-weight: bold;
    color: #333;
  }
  .el-message-box__content{
    padding: 24px 5px;
  }

  .el-message-box__btns{
    padding: 0px;

    button:nth-child(2) {
      margin-left: 20px;
    }
  }
  // .el-button{
  //   padding: 8px 35px;

  //   .el-button__text--expand{
  //     margin-right: 0px;
  //     letter-spacing: 0px;
  //   }
  // }

  
}


.primary-icon{
  color: $base-color-primary;
}