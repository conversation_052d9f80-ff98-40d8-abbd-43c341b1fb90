<template>
  <div class="firefly-vditor-component-editor" :style="{ width: '100%' }">
    <div style="display: flex">
      <div
        :style="{
          display: disabled ? 'block' : 'none',
          maxHeight: contentHeight,
          overflow: 'auto',
          position: 'relative' /* 添加相对定位 */,
        }"
        class="unified-scroll-container"
      >
        <div class="content-wrapper">
          <!-- 添加一个包装器 -->
          <slot name="content-header">
            <div></div>
          </slot>

          <article
            class="vditor-reset article-content forced-behavior"
            id="article-value"
            v-html="renderedHTML"
          ></article>
        </div>
      </div>
      <div>
        <div id="vditor-outline" v-if="needOutline && disabled"></div>
      </div>
    </div>

    <!-- 解决因使用display:none,使编辑器初始化时无法设置正常的padding -->
    <div
      :style="{
        visibility: disabled ? 'hidden' : 'visible',
        height: disabled ? '1px' : 'auto',
      }"
    >
      <div
        :style="{
          height: typeof height == 'number' ? height + 'px' : height,
          'min-height': '100px',
        }"
        v-if="state.editorIsLoading"
      >
        编辑器加载中...
      </div>
      <div :id="id" @click="clickVditor"></div>
    </div>
  </div>
</template>

<script setup>
  import {
    ref,
    onMounted,
    watch,
    onBeforeUnmount,
    reactive,
    nextTick,
  } from 'vue'
  import Vditor from 'vditor'
  import VditorPreview from 'vditor/dist/method.min'
  import 'vditor/dist/index.css'
  import { useUserStore } from '@/store/modules/user'
  import { getList as getUserlist } from '@/api/userManagement'
  import _ from 'lodash'

  const props = defineProps({
    id: {
      type: String,
      default: 'vditor',
    },
    value: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    width: {
      type: Number,
      default: 0,
    },
    height: {
      type: [Number, String],
      default: 'auto',
    },
    contentHeight: {
      type: [Number, String],
      default: '600px',
    },
    toolbar: {
      type: Array,
      default: () => [
        'headings',
        'bold',
        'strike',
        'link',
        '|',
        'list',
        'ordered-list',
        'check',
        '|',
        'quote',
        'line',
        'code',
        'inline-code',
        '|',
        'upload',
        'table',
        '|',
        'fullscreen',
        'edit-mode',
        {
          name: 'more',
          toolbar: [
            'both',
            'code-theme',
            'content-theme',
            'export',
            'outline',
            'preview',
            'devtools',
            'info',
          ],
        },
      ],
    },
    placeholder: {
      type: String,
      default:
        '请输入内容 \n支持 Markdown 语法，支持切换富文本、即时渲染和分屏预览模式',
    },
    options: {
      type: Object,
      default: () => ({}),
    },
    uploadUrl: {
      type: String,
      default: '',
    },
    type: {
      type: String,
      default: 'redmine',
    },
    needOutline: {
      type: Boolean,
      default: false,
    },
  })

  const emit = defineEmits([
    'update:value',
    'change',
    'save',
    'upload-success',
    'upload-error',
    'on-fullscreen',
  ])

  const vditor = ref(null)
  const renderedHTML = ref('')
  const userStore = useUserStore()
  const { token } = userStore

  const state = reactive({
    vditorWidth: props.width,
    editorIsLoading: true,
    isInit: true,
    queryUserTimer: null,
    options: null,
  })

  // 计算编辑器宽度
  const vditorWidth = () => {
    return props.width === 0
      ? '100%'
      : typeof state.vditorWidth === 'string'
      ? state.vditorWidth
      : state.vditorWidth + 'px'
  }

  // 处理Redmine上传接口返回结果
  const redmineUploadResult = (files, responseText) => {
    const res = JSON.parse(responseText)
    const name = files[0].name
    const url = res.data.upload.attachment.url
    const result = JSON.stringify({
      code: 0,
      data: {
        errFiles: '',
        succMap: { [name]: url },
      },
    })
    return result
  }

  // 处理BI上传接口返回结果
  const biUploadResult = (files, responseText) => {
    const res = JSON.parse(responseText)
    const name = files[0].name
    const url = res.data.full_url
    const result = JSON.stringify({
      code: 0,
      data: {
        errFiles: '',
        succMap: { [name]: url },
      },
    })
    return result
  }

  // 查询用户列表（用于@提及功能）
  const queryUserlist = async (v) => {
    let ret = []
    if (v) {
      let param = {
        filter: {
          name: v,
        },
        op: {
          name: 'LIKE',
        },
        limit: 999,
      }
      try {
        const { data } = await getUserlist(param)
        if (data.data) {
          data.data.forEach((item) => {
            ret.push({
              html: item.name,
              value: `@${item.name} `,
            })
          })
        }
      } catch (error) {
        console.error('获取用户列表失败', error)
      }
    }
    return ret
  }

  // 更新大纲
  const updateOutline = _.debounce(() => {
    if (!props.needOutline) {
      return
    }

    const outlineContainer = document.querySelector('#vditor-outline')
    if (!outlineContainer) {
      return
    }

    // 清空现有内容
    while (outlineContainer.firstChild) {
      outlineContainer.removeChild(outlineContainer.firstChild)
    }

    // 获取编辑器中的标题
    const articleElement = document.getElementById('article-value')
    if (!articleElement) {
      return
    }

    const headings = articleElement.querySelectorAll('h1, h2, h3, h4, h5, h6')

    headings.forEach((heading, index) => {
      const level = parseInt(heading.tagName.charAt(1))
      const text = heading.textContent || ''
      const id = `outline-${text.replace(/\s+/g, '-').toLowerCase()}`

      // 如果标题没有id，添加一个
      if (!heading.id) {
        heading.id = id
      }

      const child = document.createElement('div')
      child.classList.add(`vditor-outline-title${level}`)
      child.style.marginLeft = `${14 * (level - 1)}px`
      child.innerHTML = `<a href="#${heading.id}">${text}</a>`

      child.addEventListener('click', (e) => {
        e.preventDefault()
        heading.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'nearest',
        })
      })

      outlineContainer.appendChild(child)
    })
  }, 200)

  // 更新预览HTML
  const upMarkedValue = async () => {
    state.editorIsLoading = false
    try {
      // 使用Vditor的预览方法渲染HTML
      renderedHTML.value = vditor.value?.getHTML() || ''
      // 代码高亮和渲染
      const previewElement = document.getElementById('article-value')
      if (previewElement) {
        await VditorPreview.highlightRender(previewElement)
        await VditorPreview.codeRender(previewElement)

        // 更新大纲
        if (props.needOutline) {
          updateOutline()
        }

        // 添加滚动到顶部的功能
        scrollToTop()
      }
    } catch (e) {
      console.error('渲染Markdown失败', e)
    }
  }

  // 滚动到顶部的函数
  const scrollToTop = () => {
    // 找到滚动容器
    const scrollContainer = document.querySelector('.unified-scroll-container')
    if (scrollContainer) {
      // 平滑滚动到顶部
      scrollContainer.scrollTo({
        top: 0,
        behavior: 'instant',
      })
    }
  }

  // 初始化Vditor
  const initVditor = () => {
    // 合并默认选项和传入的选项
    const defaultOptions = {
      // outline: {
      //   enable: props.needOutline,
      //   position: 'right',
      // },
      mode: 'sv',
      height: props.height,
      minHeight: 100,
      placeholder: props.placeholder,
      fullscreen: {
        index: 1998,
      },
      toolbar: props.toolbar,
      upload: {
        url:
          props.uploadUrl ||
          (props.type == 'redmine' ? process.env.VUE_APP_UPLOAD_SERVER : ''),
        headers: { Authorization: `Bearer ${token}` },
        max: 10 * 1024 * 1024, // 10M
        format: props.type == 'redmine' ? redmineUploadResult : biUploadResult,
        multiple: false,
        filename(name) {
          return name
            .replace(/[^(a-zA-Z0-9\u4e00-\u9fa5.)]/g, '')
            .replace(/[?\\/:|<>*[\]()$%{}@~]/g, '')
            .replace(/\s/g, '')
        },
        fieldName: 'file',
      },
      preview: {
        minHeight: 100,
        maxWidth: 1200,
        actions: {
          click: (key) => {},
        },
        markdown: {
          autoSpace: true,
          fixTermTypo: true,
        },
        hljs: {
          style: 'monokai',
        },
      },
      hint: {
        parse: false, // 是否进行md解释，true时字符串结尾的空格会被除去
        extend: [
          {
            key: '@',
            hint: async (value) => {
              return await queryUserlist(value)
            },
          },
        ],
      },
      after: async () => {
        await nextTick()
        vditor.value?.setValue(props.value === null ? '' : props.value)
        upMarkedValue()
        state.editorIsLoading = false
      },
      blur: (v) => {
        upMarkedValue()
        emit('save', v)
      },
      input: (v) => {
        emit('update:value', v)
        emit('change', v)
        if (props.needOutline) {
          setTimeout(() => {
            updateOutline()
          }, 100)
        }
      },
      cache: {
        enable: false, // ✅ 关闭缓存  Vditor 编辑器，它会将 markdown 内容自动缓存到本地,尝试将较大内容写入 localStorage 时超出了浏览器本地存储的配额限制
      },
    }

    // 合并用户传入的选项
    state.options = _.merge(defaultOptions, props.options)

    // 设置CDN
    const cdn =
      process.env.VUE_APP_VDITOR_CDN ||
      'https://download.t-firefly.com/Source/vditor'
    if (
      cdn &&
      (typeof state.options.cdn == 'undefined' || !state.options.cdn)
    ) {
      state.options.cdn = cdn
    }

    // 初始化编辑器
    vditor.value = new Vditor(props.id, state.options)

    // 如果传入了值，则设置编辑器值
    if (props.value) {
      setValue(props.value)
      // setOutline(props.needOutline, 'right')
    }
  }

  // 点击编辑器事件（处理全屏状态）
  const clickVditor = (e) => {
    // 监听全屏操作，改变编辑器的zindex
    if (e.target && e.target.tagName) {
      let tagName = e.target.tagName.toLowerCase()
      let act = null,
        value = null,
        xlink = null
      switch (tagName) {
        case 'use':
          // #vditor-icon-contract 全屏状态时显示的图标
          xlink = e.target.getAttribute('xlink:href')
          if (
            xlink == '#vditor-icon-contract' ||
            xlink == '#vditor-icon-fullscreen'
          ) {
            act = 'fullscreen'
            value = xlink == '#vditor-icon-contract' ? false : true
          }
          break
        case 'svg':
          xlink = e.target.firstElementChild?.getAttribute('xlink:href')
          if (
            xlink == '#vditor-icon-contract' ||
            xlink == '#vditor-icon-fullscreen'
          ) {
            act = 'fullscreen'
            value = xlink == '#vditor-icon-contract' ? false : true
          }
          break
        case 'button':
          if (e.target.dataset.type == 'fullscreen') {
            act = 'fullscreen'
            let isFullscreen = false
            e.target.classList.forEach((item) => {
              if (item == 'vditor-tooltipped__sw') {
                isFullscreen = true
              }
            })
            value = isFullscreen
          }
          break
      }
      if (act === 'fullscreen') {
        emit('on-fullscreen', value)
      }
    }
  }

  // 获取编辑器的值
  const getValue = () => {
    return vditor.value?.getValue() || ''
  }

  // 设置编辑器的值
  const setValue = async (content) => {
    if (vditor.value) {
      await nextTick()
      vditor.value?.setValue(content)
      upMarkedValue()
    }
  }

  // 设置编辑器内容
  const setValueNotes = (content) => {
    if (vditor.value) {
      vditor.value?.setValue(content)
      upMarkedValue()
    }
  }

  // 获取编辑器实例
  const getInstance = () => {
    return vditor.value
  }

  // 更改工具栏显示
  const showToolbar = (show) => {
    if (vditor.value) {
      vditor.value.toolbarConfig.hide = !show
    }
  }

  // 获取编辑器Markdown值
  const getMarkdownValue = () => {
    return getValue()
  }

  // 获取编辑器HTML值
  const getHtmlValue = () => {
    return vditor.value?.getHTML() || ''
  }

  // 获取编辑器值根据传入的标志
  const getValueByType = (isGetHtml = true) => {
    return isGetHtml ? getHtmlValue() : getMarkdownValue()
  }

  /**
   * 设置大纲显示
   * @param needOutline 是否需要显示大纲
   * @param position 大纲位置，可选值：'left', 'right'，默认为'left'
   */
  const setOutline = (needOutline = false, position = 'right') => {
    if (vditor.value) {
      if (needOutline) {
        // updateOutline()
      }
    }
  }

  const setEmptyContent = () => {
    if (vditor.value) {
      vditor.value.setValue('')
      upMarkedValue()
    }
  }

  // 监听值变化
  watch(
    () => props.value,
    async (newVal) => {
      upMarkedValue()
      if (vditor.value && newVal !== getValue()) {
        await nextTick()
        setValue(newVal)
      }
    }
  )

  // 监听禁用状态变化
  watch(
    () => props.disabled,
    (newVal) => {
      if (vditor.value) {
        if (newVal) {
          vditor.value.disabled()
        } else {
          vditor.value.enable()
        }
      }
    }
  )

  onMounted(async () => {
    await nextTick()
    initVditor()
  })

  onBeforeUnmount(() => {
    // 组件销毁时释放资源
    vditor.value?.destroy()
    vditor.value = null
  })

  // 导出方法供父组件调用
  defineExpose({
    getValue,
    setValue,
    getInstance,
    upMarkedValue,
    showToolbar,
    getMarkdownValue,
    getHtmlValue,
    getValueByType,
    setOutline,
    setEmptyContent,
    setValueNotes,
  })
</script>

<style lang="scss" scoped>
  :deep(.vditor-preview__action) {
    padding: 0px;
  }

  .vditor {
    --panel-background-color: #fff;
    --textarea-background-color: #fff;

    border: 1px solid #e9e9e9;
    border-radius: 4px;

    .vditor-toolbar {
      border-bottom: 1px solid #f1f1f1;
    }

    .vditor-reset {
      font-size: 14px;
      line-height: 1.6;
    }

    .vditor-ir {
      padding: 10px 16px;
    }

    .vditor-ir__marker {
      border: 0px;
    }

    .vditor-content {
      min-height: 200px;
    }
  }

  .article-content {
    min-height: 100px;
    padding: 10px 16px;
    line-height: 1.6;

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      margin-top: 1em;
      margin-bottom: 0.5em;
      font-weight: 600;
    }

    p {
      margin: 0.65em 0;
      line-height: 1.65;
    }

    img {
      max-width: 100%;
    }

    pre {
      padding: 16px;
      overflow: auto;
      background-color: #f6f8fa;
      border-radius: 3px;
    }

    blockquote {
      padding: 0 1em;
      color: #6a737d;
      border-left: 4px solid #dfe2e5;
    }

    table {
      margin: 16px 0;
      border-collapse: collapse;

      th,
      td {
        padding: 6px 13px;
        border: 1px solid #dfe2e5;
      }

      th {
        background-color: #f6f8fa;
      }
    }
  }

  .marked pre {
    padding: 14px;
  }

  .toolbar-class {
    display: flex;
    justify-content: center;
  }

  .unified-scroll-container {
    /* 滚动容器样式 */
    background-color: #fff;
    border-radius: 4px;
  }

  .content-wrapper {
    /* 确保内容连续 */
    display: block;
    width: 100%;
  }

  /* 强制覆盖article的独立行为 */
  .forced-behavior {
    position: static !important;
    display: block !important;
    max-height: none !important;
    overflow: visible !important;
  }
</style>

<style lang="scss">
  /* 大纲容器样式 */
  #vditor-outline {
    width: calc(14vw);
    max-height: calc(100vh - 176px);
    padding: 0px 8px;
    margin-left: 20px;
    overflow: auto;
    border-left: 1px solid #dcdfe6;
  }

  // /* 大纲标题样式 */
  // #vditor-outline::before {
  //   display: block;
  //   padding-bottom: 8px;
  //   margin-bottom: 12px;
  //   font-size: 16px;
  //   font-weight: 600;
  //   color: #333333;
  //   content: '目录';
  //   border-bottom: 1px solid #e9e9e9;
  // }

  /* 大纲项样式 */
  #vditor-outline div[class^='vditor-outline-title'] {
    // margin: 2px 0;
  }

  /* 大纲链接样式 */
  #vditor-outline a {
    display: inline-block;
    width: 100%;
    height: 30px;
    padding: 0;
    margin: 0;
    overflow: hidden;
    font-size: 14px;
    line-height: 30px;
    color: #666666 !important;
    text-decoration: none;
    text-indent: 4px;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  /* 大纲链接悬停状态 */
  #vditor-outline a:hover {
    cursor: pointer;
    background-color: #334d660f;
    border-radius: 4px;
  }

  /* 大纲级别样式 */
  .vditor-outline-title1 a {
    font-size: 14px;
    font-weight: 500;
  }

  .vditor-outline-title2 a,
  .vditor-outline-title3 a,
  .vditor-outline-title4 a,
  .vditor-outline-title5 a,
  .vditor-outline-title6 a {
    font-size: 12px;
    font-weight: normal;
  }

  /* 大纲为空时的样式 */
  .outline-empty {
    padding: 10px;
    margin-top: 20px;
    font-style: italic;
    color: #999;
    text-align: center;
  }
</style>
