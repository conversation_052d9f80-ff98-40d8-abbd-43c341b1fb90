<template>
  <div
    :style="
      !customEditorUi ? { width: '100%' } : { width: '100%', height: '100%' }
    "
  >
    <head>
      <base target="_blank" />
    </head>
    <!-- <div :style="{ display: disabled ? 'block' : 'none' }">
      <article
        class="vditor-reset article-content"
        id="article-value"
        v-html="state.html"
      ></article>
    </div> -->
    <!-- 解决因使用display:none,使编辑器初始化时无法设置正常的padding -->
    <!--20231225为显示tooltip删除 overflow: disabled ? 'hidden' : 'auto', -->
    <!-- <div :id="divId" @click="clickVditor"></div> -->
    <div
      v-if="!customEditorUi"
      ref="divRef"
      :style="{ height: height }"
      class="aie-theme-light"
      :class="disabled ? 'hide-top-bar' : ''"
    >
      <div ref="container" class="aie-container">
        <div
          ref="header"
          class="aie-container-header"
          style="position: sticky; top: -20px; z-index: 999; background: white"
        ></div>
      </div>
    </div>
    <div v-else style="height: 100%">
      <div
        ref="divRef"
        id="aiEditor"
        class="aie-theme-light"
        :class="disabled ? 'hide-top-bar' : ''"
        style="height: 100%"
      >
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, reactive } from 'vue'
  import { useUserStore } from '@/store/modules/user'
  import { getList as getUserlist } from '@/api/userManagement'
  import Vditor from 'vditor'
  import 'vditor/dist/index.css'
  import _ from 'lodash'
  import { UploadServer } from '@/api/setting'
  import { debounce } from 'lodash'
  import pinyin from 'pinyin'

  import { AiEditor, AiEditorOptions } from 'aieditor'
  // import {
  //   AiEditor,
  //   AiEditorOptions,
  // } from '/home/<USER>/Tchipbi/frontend/aieditor/AiEditor/src/core/AiEditor'
  // import {
  //   AiEditor,
  //   AiEditorOptions,
  // } from '/home/<USER>/Tchipbi/frontend/aieditor/AiEditor_v1.2.5/AiEditor/src/core/AiEditor'

  // import '~/src/utils/aieditor/dist/style.css'

  const divRef = ref()
  let aiEditor: AiEditor | null = null

  const props = defineProps({
    contentType: {
      // 用以判断setContent所设置的文本数据原类型，防止html下code块样式set进去时候出现错乱
      type: String,
      default: 'markdown',
    },
    mentionMember: {
      type: Array,
      default: () => {
        return []
      },
    },
    customEditorUi: {
      type: Boolean,
      defalut: false,
    },

    divId: {
      type: String,
      default: 'vditor',
    },
    value: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    width: {
      type: Number,
      default: 0,
    },
    height: {
      type: [Number, String],
      default: 'auto',
    },
    toolbarKeys: {
      type: Array as () => AiEditorOptions['toolbarKeys'],
      default: () => {
        return [
          'brush',
          'eraser',
          'heading',
          'font-size',
          '|',
          'bold',
          'italic',
          'underline',
          'strike',
          'link',
          'code',
          // 'subscript',
          // 'superscript',
          'hr',
          'todo',
          '|',
          'highlight',
          'font-color',
          'align',

          '|',
          'bullet-list',
          'ordered-list',
          'indent-decrease',
          'indent-increase',
          '|',
          'image',
          'video',
          'attachment',
          'quote',
          'code-block',
          'table',
          'fullscreen',
        ]
      },
    },

    options: {
      type: Object,
      default: () => {
        return null
      },
    },
    placeholder: {
      type: String,
      default: '请输入内容 \n支持 Markdown 语法，支持富文本',
    },
    type: {
      type: String,
      default: 'redmine',
    },
  })

  // 内容 HTML
  const userStore = useUserStore()
  const { token } = userStore

  const { disabled, value, mentionMember } = toRefs(props)

  const emit = defineEmits([
    'update:value',
    'on-fullscreen',
    'on-upload-success',
    'on-upload-before',
    'content-on-change',
  ])

  const state: any = reactive({
    userList: props.mentionMember ?? [],
  })

  //上传服务接口
  const uploadServerUrl =
    props.type == 'redmine' ? process.env.VUE_APP_UPLOAD_SERVER : UploadServer

  const uploadHeaders = {
    Authorization: `Bearer ${token}`,
  }
  const unploader = (
    file: File,
    uploadUrl: string,
    headers: Record<string, any>,
    formName: string
  ) => {
    //自定义附件上传逻辑
    const formData = new FormData()
    formData.append('file', file)
    return new Promise<Record<string, any>>((resolve, reject) => {
      fetch(uploadUrl, {
        method: 'post',
        headers: { Accept: 'application/json', ...headers },
        body: formData,
      })
        .then((resp) => resp.json())
        .then((json) => {
          resolve(json)
        })
        .catch((error) => {
          reject(error)
        })
    })
  }
  //上传钩子
  const uploaderEvent = (uploadType?: string) => {
    return {
      onUploadBefore: (
        file: File,
        uploadUrl: string,
        headers: Record<string, any>
      ) => {
        emit('on-upload-before')
      },
      onSuccess: (file: File, response: any) => {
        if (response.code != 200) {
          return false
        }
        emit('on-upload-success', response)
        let url = null
        if (props.type == 'redmine') {
          url = response.data.upload.attachment.url
        } else {
          url = response.data.full_url
        }
        if (url) {
          const result = {
            errorCode: 0,
            data: {
              src: url,
              alt: file.name,
              href: '',
              fileName: '',
            },
          }
          //不同上传类型，返回的结构数据不一样，视频和图片只是多了封面图片，故不判断
          if (uploadType === 'attachment') {
            result.data['href'] = url
            result.data['fileName'] = file.name
          }

          return result
        } else {
          return false
        }
      },
      onFailed: (file: File, response: any) => {
        //监听图片上传失败，或者返回的 json 信息不正确
      },
      onError: (file: File, error: any) => {
        //监听图片上传错误，比如网络超时等
      },
    }
  }

  /**
   * 左侧目录
   * @param editor
   */
  const updateOutLine = debounce((editor: AiEditor) => {
    const outlineContainer = document.querySelector('#outline')
    while (outlineContainer?.firstChild) {
      outlineContainer.removeChild(outlineContainer.firstChild)
    }

    const outlines = editor.getOutline()
    for (let outline of outlines) {
      const child = document.createElement('div')
      child.classList.add(`aie-title${outline.level}`)
      child.style.marginLeft = `${14 * (outline.level - 1)}px`
      child.innerHTML = `<a href="#${outline.id}">${outline.text}</a>`
      child.addEventListener('click', (e) => {
        e.preventDefault()
        const el = editor.innerEditor.view.dom.querySelector(
          `#${outline.id}`
        ) as HTMLElement
        el.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'nearest',
        })
        setTimeout(() => {
          editor.focusPos(outline.pos + outline.size - 1)
        }, 1000)
      })
      outlineContainer?.appendChild(child)
    }
  }, 200)

  const selectedItemRef = ref<HTMLElement | null>(null)

  const scrollToSelectedItem = () => {
    if (selectedItemRef.value) {
      selectedItemRef.value.scrollIntoView({
        behavior: 'instant',
        block: 'nearest',
      })
    }
  }

  const updateSelectedItem = () => {
    const rootElement = document.querySelector('[data-tippy-root]')
    if (rootElement) {
      const selectedItem = rootElement.querySelector(
        '.item.item-selected'
      ) as HTMLElement | null
      selectedItemRef.value = selectedItem
      scrollToSelectedItem()
    }
  }

  const handleKeyDown = (event: KeyboardEvent) => {
    if (event.key === 'ArrowUp' || event.key === 'ArrowDown') {
      updateSelectedItem()
    }
  }

  const observerCallback: MutationCallback = (mutationsList) => {
    for (const mutation of mutationsList) {
      if (
        mutation.type === 'attributes' &&
        (mutation.target as HTMLElement).classList.contains('item-selected')
      ) {
        updateSelectedItem()
      }
    }
  }

  let observer: MutationObserver

  const observerAIEditor = () => {
    const rootElement = document.querySelector('.aie-container')
    if (rootElement) {
      observer = new MutationObserver(observerCallback)
      observer.observe(rootElement, {
        attributes: true,
        childList: false,
        subtree: true,
        attributeFilter: ['class'],
      })

      document.addEventListener('keydown', handleKeyDown)

      updateSelectedItem()
    }
  }

  onMounted(async () => {
    observerAIEditor()

    // 先设置初始用户列表
    if (props.mentionMember.length > 0) {
      state.userList = props.mentionMember
    } else {
      state.userList = [] // 初始为空数组，异步加载
    }

    // 立即初始化编辑器，不等待用户列表加载
    aiEditor = new AiEditor({
      element: divRef.value as Element,
      placeholder: props.placeholder,
      toolbarKeys: props.toolbarKeys,
      image: {
        allowBase64: true,
        defaultSize: 300,
        uploadUrl: uploadServerUrl,
        uploadHeaders: uploadHeaders,
        uploader: unploader,
        uploaderEvent: uploaderEvent(),
      },

      video: {
        uploadUrl: uploadServerUrl,
        uploadHeaders: uploadHeaders,
        uploader: unploader,
        uploaderEvent: uploaderEvent(),
      },

      attachment: {
        uploadUrl: uploadServerUrl,
        uploadHeaders: uploadHeaders,
        uploader: unploader,
        uploaderEvent: uploaderEvent('attachment'),
      },

      onMentionQuery: async (query: string) => {
        const queryInitials = getInitials(query)
        return state.userList
          .filter((item: any) => item.includes(query)) // 先筛选中文字符匹配的结果
          .concat(
            state.userList.filter((item: any) => {
              const itemInitials = getInitials(item)
              return (
                !item.includes(query) && itemInitials.includes(queryInitials)
              )
            })
          )
          .slice(0, 200)
      },
      onChange: (aiEditor) => {
        // 监听到用编辑器内容发生变化了
        emit('content-on-change', getHtmlValue())
        updateOutLine(aiEditor)
      },
      onCreated: (editor) => {
        updateOutLine(editor)
      },

      ai: {
        models: {},
        commands: [],
      },
      link: {
        autolink: false,
      },
    })

    nextTick(() => {
      // 需要 disabled 默认为false，调用顺序需要注意
      if (disabled.value) {
        aiEditor?.setEditable(false)
        setValueNotes(props.value)
        // aiEditor?.setContent(props.value, props.contentType)
        return
      }
      setValueNotes(props.value)
      // aiEditor?.setContent(props.value, props.contentType)
      aiEditor?.setEditable(!disabled.value)
      checkOverflow(true)
    })

    // 异步加载用户列表，不阻塞编辑器初始化
    if (props.mentionMember.length === 0) {
      loadUserListAsync()
    }

    window.addEventListener('wheel', checkOverflow)
  })

  // 新增：异步加载用户列表的函数
  const loadUserListAsync = async () => {
    try {
      const res = await queryUserlist('')
      state.userList = res.map((item: any) => {
        return item.value
      })
    } catch (error) {
      console.error('加载用户列表失败:', error)
      // 加载失败时保持空数组，不影响编辑器功能
      state.userList = []
    }
  }

  onUnmounted(() => {
    aiEditor && aiEditor.destroy()

    if (observer) {
      observer.disconnect()
      document.removeEventListener('keydown', handleKeyDown)
    }

    window.removeEventListener('wheel', checkOverflow)
  })

  const vditor = ref<Vditor | null>(null)

  interface IHintData {
    id: any
    value: string
  }

  const queryUserlist = async (v: string) => {
    let ret: IHintData[] = []
    let param = {
      filter: {
        name: v,
      },
      op: {
        name: 'LIKE',
      },
      limit: 999,
    }
    const { data } = await getUserlist(param)
    if (data.data) {
      data.data.forEach((item: any) => {
        ret.push({
          id: item.id,
          value: `${item.name} `,
        })
      })
    }
    return ret
  }

  ///获取编辑器的html
  const getValueNotes = () => {
    // ![](clipboard-202402221644-n6f36.png)
    let markdown = aiEditor?.getMarkdown()

    // 检查 markdown 是否为有效值，如果不是则返回空字符串
    if (!markdown) {
      return ''
    }

    // 把带有<>的超链接替换为 Markdown 格式的超链接
    let pattern = /<http[s]?:\/\/([^>]+)>/g
    let sea = markdown.match(pattern)
    if (sea && sea.length > 0) {
      sea.forEach((mitem: string) => {
        let newUrl = mitem.replace('<', '').replace('>', '')
        markdown = markdown.replace(mitem, `[${newUrl}](${newUrl})`)
      })
    }
    return markdown //获取为markdown语法内容，为在redmine那边展示
  }

  const getHtmlValue = () => {
    return aiEditor?.getHtml()
  }

  const getMarkdownValue = () => {
    return aiEditor?.getMarkdown()
  }

  const getTextValue = () => {
    return aiEditor?.getText()
  }

  const getValueByType = (isGetHtml = true) => {
    return isGetHtml ? getHtmlValue() : getMarkdownValue()
  }

  ///设置编辑器的html
  const setValueNotes = (content: string) => {
    if (props.contentType == 'markdown') {
      return aiEditor?.setMarkdownContent(content)
    }
    return aiEditor?.setContent(content, props.contentType)
  }

  const setEmptyContent = () => {
    if (aiEditor) {
      setValueNotes('')
    }
  }

  /**
   * 中文转拼音首字母
   */
  const getInitials = (name: string) => {
    const py = pinyin(name, { style: pinyin.STYLE_FIRST_LETTER })
    return py.map((item: string) => item[0]).join('') // 提取首字母
  }

  watch(value, async (v) => {
    if (props.customEditorUi) {
      // 下面的方法会使编辑器失去焦点
      return
    }
    setValueNotes(v)
    // aiEditor?.setContent(v, props.contentType)
  })

  watch(disabled, (v) => {
    aiEditor?.setEditable(!v)
  })

  // onMounted(async () => {
  //   initVditor()
  //   vditor.value = new Vditor(props.divId, state.options)
  //   if (props.value) {
  //     state.html = await getValueHtml(props.value)
  //     upMarkedValue()
  //   }
  // })

  const container = ref()
  const header = ref()

  const checkOverflow = _.throttle(async (firstRun = false) => {
    if (firstRun) {
      // console.log('await 200ms')
      await new Promise((resolve) => setTimeout(resolve, 300))
    }
    if (props.customEditorUi || props.disabled) {
      // 非编辑状态
      return
    }
    const containerRect = container?.value?.getBoundingClientRect()

    const tips: HTMLElement[] = []
    const editorProseMirrorSelectednode = document.querySelector(
      '.tippy-box'
    ) as HTMLElement

    // 暂时只对选中文本后弹出的tip做监听修改 防止影响到height较小的editor中如代码块语言选中的滚动效果
    const bubbleText = editorProseMirrorSelectednode?.querySelector(
      'aie-bubble-text'
    ) as HTMLElement | null

    // if (editorProseMirrorSelectednode) {
    //   tips.push(editorProseMirrorSelectednode)
    // }
    if (bubbleText) {
      tips.push(bubbleText)
    }
    // console.log(tips)

    tips.forEach((tip: HTMLElement) => {
      // 先暂时显示元素
      tip.style.display = 'flex'

      const tipRect = tip.getBoundingClientRect()
      tip.style.display = 'none'

      // console.log('tipRect:', tipRect)

      const isVisible =
        tipRect.top > 0 &&
        tipRect.bottom > 0 &&
        tipRect.top >= containerRect.top - 200 &&
        tipRect.bottom <= containerRect.bottom + 100

      if (isVisible) {
        tip.style.display = 'flex'
      } else {
        tip.style.display = 'none'
      }
    })
  }, 1) // 节流时间可以根据需要调整

  // 组件即将销毁
  onBeforeUnmount(() => {
    vditor.value?.destroy()
    vditor.value = null
  })

  defineExpose({
    getValueNotes,
    setValueNotes,
    getHtmlValue,
    getMarkdownValue,
    getTextValue,
    getValueByType,
    setEmptyContent,
  })
</script>
<style lang="scss">
  [data-tippy-root] {
    .items {
      max-height: 400px;
      overflow: scroll !important;
    }

    .items .item {
      &:hover {
        background-color: #eee;
      }
    }
  }
  .aie-codeblock-tools-comments,
  .aie-codeblock-tools-explain {
    display: none !important; //点击自动注释会导致出问题
  }
  .hide-top-bar {
    aie-header {
      display: none;
    }
    .aie-container {
      border: none;
    }
    .aie-content {
      padding: 0;
      border: none;
    }
  }
  aie-footer {
    display: none;
  }
  .aie-content {
    min-height: 200px;
  }
  .aie-container {
    max-height: 450px;
  }
  .aie-container * {
    box-sizing: content-box !important;
  }
  .aie-content span[data-type='mention'] {
    color: $base-color-primary;
    border: 0;
  }
  .aie-content p {
    margin: 0 !important;
    margin-top: 0.65em !important;
    margin-bottom: 0.65em !important;
    line-height: 1.65 !important;
  }
  .aie-container .aie-codeblock-wrapper {
    line-height: 1.5;
  }

  .aie-content h1,
  .aie-content h2,
  .aie-content h3,
  .aie-content h4,
  .aie-content h5,
  .aie-content h6 {
    padding-top: 0px !important;
    margin: 16px 0 0px !important;
    margin-block-start: 9px !important;
    margin-block-end: 18px !important;
    line-height: 1.1 !important;
  }
  .aie-content h1 {
    font-size: 2em !important;
  }
  .aie-content h2 {
    font-size: 1.5em !important;
  }
  .aie-content h3 {
    font-size: 1.17em !important;
  }
  .aie-content h4 {
    font-size: 1em !important;
  }
  .aie-content h5 {
    font-size: 0.83em !important;
  }
  .aie-content h6 {
    font-size: 0.67em !important;
  }
  .aie-content ul,
  .aie-content ol {
    margin-top: 0.25rem !important;
    margin-bottom: 0.25rem !important;
  }
  .aie-content ul {
    padding: 0 1.22rem !important;
  }
  .aie-content ol {
    padding: 0 1.12rem !important;
  }
  .aie-content hr {
    margin: 2rem 0;
    border: none;
    border-top: 2px solid #ced4da;
  }
  .aie-content .tableWrapper {
    padding: 0px;
  }
</style>
<style lang="scss" scoped>
  :deep() {
    a {
      color: #116699 !important;
    }
    .vditor-ir__marker {
      border: 0px;
    }
    .marked {
      pre {
        padding: 14px;
      }
    }
    .vditor-content {
      min-height: 200px;
    }
  }
  .toolbar-class {
    display: flex;
    justify-content: center;
  }
</style>
