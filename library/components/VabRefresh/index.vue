<script lang="ts" setup>
  import { useSettingsStore } from '@/store/modules/settings'

  const $pub: any = inject('$pub')

  const settingsStore = useSettingsStore()
  const { theme } = storeToRefs(settingsStore)

  const refreshRoute = () => {
    $pub('reload-router-view')
  }
</script>

<template>
  <div v-if="theme.showRefresh" class="refresh-btn" @click="refreshRoute">
    <vab-icon icon="refresh-line" />
    <span>刷新</span>
  </div>
</template>

<style lang="scss" scoped>
  .refresh-btn {
    display: flex;
    align-items: center;
    cursor: pointer;
  }
</style>
