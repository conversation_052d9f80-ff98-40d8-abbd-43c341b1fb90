<script setup>
  import { ref, reactive, computed, watch } from 'vue'
  import { useSettingsStore } from '@/store/modules/settings'
  import { storeToRefs } from 'pinia'
  import { ArrowDown } from '@element-plus/icons-vue'
  import { doEdit } from '@/api/userNoticePush'
  import { getUserNoticeList, getUserNoticeType } from '@/api/userNoticePush'

  const $baseMessage = inject('$baseMessage')
  const $baseConfirm = inject('$baseConfirm')

  const props = defineProps({
    drawerVisible: {
      type: Boolean,
      default: undefined,
    },
  })
  const emit = defineEmits(['update:drawerVisible', 'message-read'])
  const visible = useVModel(props, 'drawerVisible', emit)

  // 是否只显示未读消息
  const onlyUnread = ref(false)

  // 分页相关
  const pageInfo = reactive({
    pageNo: 1,
    pageSize: 10,
    pageSizes: [10, 20, 50, 100],
    total: 0,
  })

  // 消息数据
  const allMessages = ref([])
  // 本地保存完整的消息数据
  const messageData = ref({ data: [], total: 0 })

  // 获取菜单收缩状态
  const settingsStore = useSettingsStore()
  const { collapse } = storeToRefs(settingsStore)

  // 颜色映射表
  const COLOR_MAP = {
    system: '#F56C6C',
    product: '#E6A23C',
    project: '#409EFF',
    issue_journal: '#67C23A',
    issue_at: '#9966FF',
    production: '#1E90FF',
  }

  // 消息类型列表
  const messageTypes = ref([
    { key: 'unread', label: '未读消息', type: 'unread' }, // 默认显示未读消息
    { key: 'all', label: '全部消息', type: 'all' },
  ])

  // 当前选中的消息类型
  const activeType = ref('unread')

  // 加载状态
  const loading = ref(false)

  // 添加一个控制弹窗显示的状态
  const dialogVisible = ref(false)
  // 当前查看的消息
  const currentMessage = ref(null)

  // 处理消息类型切换
  const handleTypeChange = async (type) => {
    activeType.value = type
    pageInfo.pageNo = 1 // 切换类型时重置页码
    await getMessageData()
  }

  // 获取消息列表数据的方法
  const getMessageData = async () => {
    try {
      loading.value = true
      const response = await getUserNoticeList({
        filter: {
          notice_mode: 'website',
          status: 1,
          // 未读筛选（仅当类型为 'unread' 时生效）
          ...(activeType.value === 'unread' ? { is_read: 0 } : {}),
          // 类型筛选（仅当类型不为 'all'/'unread' 时生效）
          ...(activeType.value !== 'all' && activeType.value !== 'unread'
            ? { notice_type: activeType.value }
            : {}),
        },
        page: pageInfo.pageNo,
        limit: pageInfo.pageSize,
      })

      // 保存获取到的消息数据
      messageData.value = response.data || { data: [], total: 0 }

      // 处理消息数据
      await fetchMessages()
    } catch (error) {
      console.error('获取消息数据失败:', error)
      $baseMessage('获取消息失败', 'error')
    } finally {
      loading.value = false
    }
  }

  const initMessageTypes = async () => {
    try {
      const response = await getUserNoticeType()

      messageTypes.value = [
        { key: 'unread', label: '未读消息', type: 'unread' },
        { key: 'all', label: '全部消息', type: 'all' },
        ...response.data.map((item) => ({
          key: item.type,
          label: item.label,
          type: item.type,
        })),
      ]
    } catch (error) {
      console.error('获取消息类型失败:', error)
    }
  }

  // 修改fetchMessages方法，使用本地数据
  const fetchMessages = async () => {
    try {
      // 使用本地保存的消息数据
      const apiMessages = messageData.value.data || []

      // 清空原有数据
      allMessages.value = []

      // 颜色映射表
      const colorMap = {
        system: '#F56C6C',
        product: '#E6A23C',
        project: '#409EFF',
        issue_journal: '#67C23A',
        issue_at: '#9966FF',
        production: '#1E90FF',
      }

      // 处理并添加消息
      apiMessages.forEach((item) => {
        // 获取标签文本（使用mode_text或根据类型生成）
        const labelText = item.mode_text || `${item.notice_type}消息`

        // 获取头像文字（取mode_text的第一个字）
        let avatarText = labelText.charAt(0)
        // 如果是AT开头，使用@符号
        if (avatarText === 'A' && item.notice_type === 'issue_at') {
          avatarText = '@'
        }

        // 获取颜色
        const avatarColor = colorMap[item.notice_type] || '#909399'

        // 转换为组件需要的数据格式
        allMessages.value.push({
          id: item.id,
          type: item.notice_type,
          mode_text: item.mode_text,
          title: item.notice_title,
          time: item.created_day,
          avatar: avatarText,
          avatarColor,
          content: item.content.replace(/<[^>]*>/g, ''), // 移除HTML标签用于列表显示
          rawContent: item.content, // 保留原始内容，包含HTML标签
          isRead: item.is_read === 1,
        })
      })

      // 更新分页信息
      pageInfo.total = messageData.value.total || 0
    } catch (error) {
      console.error('处理消息数据失败:', error)
    }
  }

  // 修改分页方法
  const handleSizeChange = async (val) => {
    pageInfo.pageSize = val
    await getMessageData() // 直接获取新的数据
  }

  const handleCurrentChange = async (val) => {
    pageInfo.pageNo = val
    await getMessageData() // 直接获取新的数据
  }

  // 筛选消息 - 使用分页
  // const filteredMessages = computed(() => {
  //   let result = [...allMessages.value]

  //   // 按类型筛选
  //   if (activeType.value !== 'all') {
  //     result = result.filter((msg) => msg.type === activeType.value)
  //   }

  //   // 按读取状态筛选
  //   if (onlyUnread.value) {
  //     result = result.filter((msg) => !msg.isRead)
  //   }

  //   return result
  // })

  // 获取当前选中类型的标签文本
  const activeTypeLabel = computed(() => {
    const found = messageTypes.value.find(
      (type) => type.key === activeType.value
    )
    return found ? found.label : '全部消息'
  })

  // 切换未读消息筛选
  const toggleUnreadFilter = () => {
    onlyUnread.value = !onlyUnread.value
    fetchMessages()
  }

  // 修改处理消息点击的函数
  const handleMessageClick = async (message) => {
    // 标记为已读
    if (!message.isRead) {
      try {
        await doEdit({
          id: message.id,
          is_read: 1,
        })
        message.isRead = true

        // 更新父组件的消息数据
        if (messageData.value && messageData.value.data) {
          const originalMessage = messageData.value.data.find(
            (item) => item.id === message.id
          )
          if (originalMessage) {
            originalMessage.is_read = 1
          }
        }

        emit('message-read', { isMarkAll: false, count: 1 })
      } catch (error) {
        console.error('更新消息已读状态失败:', error)
        $baseMessage('标记已读失败', 'error')
      }
    }

    // 设置当前消息并打开弹窗
    currentMessage.value = message
    dialogVisible.value = true
  }

  // 全部标记为已读函数也需要更新
  const markAllAsRead = async () => {
    try {
      loading.value = true
      // 获取所有未读消息
      const response = await getUserNoticeList({
        filter: {
          notice_mode: 'website',
          status: 1,
          is_read: 0, // 只获取未读消息
        },
        page: 1,
        limit: 1000, // 使用较大的限制以获取尽可能多的未读消息
      })

      const allUnreadMessages = response.data?.data || []
      const unreadIds = allUnreadMessages.map((message) => message.id)

      // 如果没有未读消息，直接返回
      if (unreadIds.length === 0) {
        loading.value = false
        return
      }

      // 批量更新消息为已读状态
      await Promise.all(
        unreadIds.map((id) =>
          doEdit({
            id: id,
            is_read: 1,
          })
        )
      )

      // 更新本地状态
      allMessages.value.forEach((message) => {
        message.isRead = true
      })

      // 通知父组件消息已读
      emit('message-read', { isMarkAll: true, count: unreadIds.length })

      $baseMessage('消息已标记为已读', 'success')

      // 切换到全部消息
      activeType.value = 'all'
      await getMessageData() // 重新获取全部消息数据

      // 重新获取当前页面的消息数据以更新UI
      await getMessageData()
    } catch (error) {
      console.error('批量标记已读失败:', error)
      $baseMessage('批量标记已读失败', 'error')
    } finally {
      loading.value = false
    }
  }

  // 为所有没有target属性的a标签添加target="_blank"
  const processedContent = computed(() => {
    if (!currentMessage.value?.rawContent) return ''

    return currentMessage.value.rawContent.replace(
      /<a\s+([^>]*?)>/gi,
      (match, attributes) => {
        // 如果已经有target属性，则不修改
        if (/target\s*=/i.test(attributes)) {
          return match
        }
        // 添加target="_blank"和rel="noopener noreferrer"（安全考虑）
        return `<a ${attributes} target="_blank" rel="noopener noreferrer">`
      }
    )
  })

  // 处理抽屉打开的事件
  const handleDrawerOpen = async () => {
    await getMessageData() // 打开抽屉时获取消息数据
    await initMessageTypes() // 初始化消息类型

    if (activeType.value === 'unread' && allMessages.value.length === 0) {
      activeType.value = 'all'
      await getMessageData() // 重新获取全部消息数据
    }
  }

  // 获取筛选后的消息总数
  const getFilteredTotal = () => {
    return pageInfo.total
  }
</script>

<template>
  <div>
    <el-drawer
      v-model="visible"
      direction="ltr"
      @close="$emit('update:drawerVisible', false)"
      @open="handleDrawerOpen"
      append-to-body
      :show-close="false"
      size="520px"
      custom-class="bi-notice-drawer"
      :modal-class="
        collapse ? 'bi-notice-modal-collapsed' : 'bi-notice-modal-expanded'
      "
    >
      <template #header>
        <div class="bi-notice-message-header">
          <div class="bi-notice-header-left">
            <el-dropdown @command="handleTypeChange" trigger="click">
              <div style="display: flex; align-items: center; cursor: pointer">
                <div class="bi-notice-header-title">{{ activeTypeLabel }}</div>
                <el-icon style="margin-left: 4px"><arrow-down /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu v-loading="loading">
                  <el-dropdown-item
                    v-for="type in messageTypes"
                    :key="type.key"
                    :command="type.key"
                  >
                    {{ type.label }}
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>

          <div class="bi-notice-header-right">
            <el-button
              type="primary"
              size="small"
              @click="markAllAsRead"
              :disabled="loading || allMessages.every((msg) => msg.isRead)"
            >
              一键已读
            </el-button>
          </div>
        </div>
      </template>

      <div class="bi-notice-message-container">
        <!-- 消息列表 -->
        <div class="bi-notice-message-list">
          <div
            v-for="message in allMessages"
            :key="message.id"
            class="bi-notice-message-item"
            :class="{
              'bi-notice-unread': !message.isRead,
            }"
            @click="handleMessageClick(message)"
          >
            <!-- 消息头部：标题和时间 -->
            <div class="bi-notice-message-item-header">
              <div class="bi-notice-message-avatar-container">
                <div
                  class="bi-notice-message-avatar"
                  :style="{ backgroundColor: message.avatarColor }"
                >
                  {{ message.avatar }}
                </div>
                <div v-if="!message.isRead" class="bi-notice-unread-dot"></div>
              </div>
              <div class="bi-notice-message-title">{{ message.mode_text }}</div>
              <div class="bi-notice-message-time">{{ message.time }}</div>
            </div>

            <!-- 消息内容 -->
            <div class="bi-notice-message-content">
              <!-- 发送者 -->
              <!--  <span class="bi-notice-message-sender">{{ message.sender }}</span> -->
              <!-- 内容 -->
              <span class="bi-notice-message-text">{{ message.content }}</span>
            </div>
          </div>

          <!-- 无消息提示 -->
          <div v-if="allMessages.length === 0" class="bi-notice-no-message">
            <p>暂无消息</p>
          </div>
        </div>

        <!-- 分页组件 -->
        <div class="bi-notice-pagination-container">
          <el-pagination
            background
            :current-page="pageInfo.pageNo"
            :page-size="pageInfo.pageSize"
            :page-sizes="pageInfo.pageSizes"
            :layout="'total, sizes, prev, jumper, slot, next'"
            :total="getFilteredTotal()"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
          >
            <template #default>
              / {{ Math.ceil(getFilteredTotal() / pageInfo.pageSize) }} 页
            </template>
          </el-pagination>
        </div>
      </div>
    </el-drawer>

    <firefly-dialog
      v-model="dialogVisible"
      :title="null"
      :destroy-on-close="true"
      :close-on-click-modal="true"
      class="bi-notice-message-auto-dialog"
      @close="dialogVisible = false"
    >
      <template #header>
        <div class="bi-notice-message-custom-header">
          <div class="bi-notice-message-detail-header">
            <div
              class="bi-notice-message-detail-avatar"
              :style="{ backgroundColor: currentMessage?.avatarColor }"
            >
              {{ currentMessage?.avatar }}
            </div>
            <div class="bi-notice-message-detail-info">
              <div class="bi-notice-message-detail-title">
                {{ currentMessage?.mode_text }}
              </div>
              <div class="bi-notice-message-detail-time">
                {{ currentMessage?.time }}
              </div>
            </div>
          </div>
        </div>
      </template>
      <el-divider />
      <div class="bi-notice-message-detail-content">
        <!-- 消息详细内容 -->
        <div class="bi-notice-message-detail-body">
          <div class="bi-notice-message-detail-title" style="font-size: 14px">
            {{ currentMessage?.title }}
          </div>
          <div
            class="bi-notice-message-detail-content-text"
            v-html="processedContent"
          ></div>
        </div>
      </div>
    </firefly-dialog>
  </div>
</template>

<style lang="scss" scoped>
  .bi-notice-message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    margin-bottom: 0;

    .bi-notice-header-left {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .bi-notice-header-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    .bi-notice-dropdown-link {
      color: #333;
      font-size: 14px;
    }

    .bi-notice-unread-filter {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      color: #333;
    }
  }

  .bi-notice-message-container {
    flex: 1;
    overflow-y: auto;
    position: relative;
    display: flex;
    flex-direction: column;
  }

  .bi-notice-message-list {
    flex: 1;
    overflow-y: auto;
    padding: 0 20px;
    /* 隐藏滚动条 - WebKit浏览器 (Chrome, Safari, Edge) */
    &::-webkit-scrollbar {
      width: 0;
      background: transparent;
    }

    /* 隐藏滚动条 - Firefox */
    scrollbar-width: none;

    /* 隐藏滚动条 - IE */
    -ms-overflow-style: none;
  }

  .bi-notice-message-item {
    padding: 18px 20px;
    border-bottom: 1px solid #dcdfe6;
    background-color: transparent;
    cursor: pointer;

    &:hover {
      background-color: #f9f9f9;
    }

    &.bi-notice-unread {
      position: relative;
    }
  }

  .bi-notice-message-item-header {
    display: flex;
    align-items: center;

    .bi-notice-message-avatar-container {
      position: relative;
      margin-right: 8px;
      flex-shrink: 0;
    }

    .bi-notice-message-avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      flex-shrink: 0;
    }

    .bi-notice-message-title {
      font-size: 14px;
      font-weight: 600;
      color: #333;
      flex: 1;
    }

    .bi-notice-message-time {
      font-size: 14px;
      color: #999;
      margin-left: auto;
    }

    .bi-notice-unread-dot {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background-color: #ff5e4b;
      position: absolute;
      top: 0px;
      right: -2px;
      border: 1px solid #fff;
      z-index: 1;
    }
  }

  .bi-notice-message-content {
    padding-left: 40px;
    font-size: 14px;
    line-height: 1.57;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }

  .bi-notice-message-sender {
    color: #333;
    font-weight: 400;
    margin-right: 4px;
    display: inline;
  }

  .bi-notice-message-text {
    color: #666;
    word-break: break-word;
    display: inline;
  }

  .bi-notice-no-message {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px 0;
    color: #999;
    font-size: 14px;
  }

  .bi-notice-pagination-container {
    flex-shrink: 0;
    padding: 15px 20px;
    background-color: #fff;
    display: flex;
    justify-content: center;

    :deep(.el-pagination) {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      white-space: nowrap;

      .el-pagination__total {
        flex: 1;
        margin-right: 20px;
      }

      .el-pagination__sizes {
        flex: 1;
        text-align: center;
        margin: 0 10px;
      }

      .btn-prev,
      .el-pagination__jump,
      .slot,
      .btn-next {
        flex: 1;
        text-align: right;
        display: inline-flex;
        align-items: center;
      }

      .el-pagination__jump {
        margin-left: 0;
        .el-pagination__editor {
          width: 50px;
        }
      }

      .el-select .el-input {
        width: 110px;
      }

      /* 移除箭头按钮的背景色 */
      &.is-background .btn-prev,
      &.is-background .btn-next {
        background-color: transparent;
        border: none;

        &:hover {
          color: var(--el-color-primary);
          background-color: transparent;
        }

        &:disabled {
          color: var(--el-disabled-text-color);
          background-color: transparent;
        }
      }
    }
  }

  .bi-notice-message-custom-header {
    display: flex;
    align-items: center;
    width: 100%;
    padding-right: 40px;
  }

  .bi-notice-message-detail-header {
    display: flex;
    align-items: center;
    width: 100%;
  }

  .bi-notice-message-detail-avatar {
    width: 46px;
    height: 46px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 22px;
    margin-right: 15px;
    flex-shrink: 0;
  }

  .bi-notice-message-detail-info {
    flex: 1;
  }

  .bi-notice-message-detail-title {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    margin-bottom: 5px;
  }

  .bi-notice-message-detail-time {
    font-size: 14px;
    color: #999;
  }

  .bi-notice-message-detail-content {
    padding: 0 20px 20px;
  }

  .bi-notice-message-detail-body {
    font-size: 14px;
    line-height: 1.8;
    color: #666;
    margin-top: 10px;
  }

  .bi-notice-message-detail-content-text {
    :deep(p) {
      margin-bottom: 10px;
    }

    :deep(a) {
      color: var(--el-color-primary);
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .bi-notice-message-detail-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 30px;
  }
</style>

<style lang="scss">
  .bi-notice-drawer {
    box-shadow: 10px 0 10px -5px rgba(0, 0, 0, 0.1);
    .el-drawer__header {
      margin-bottom: 10px;
    }

    .el-drawer__body {
      padding: 0;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      height: 100%;
    }
  }

  .bi-notice-modal-collapsed {
    left: 72px !important;
    width: calc(100% - 72px) !important;

    .el-drawer.ltr {
      left: 0 !important;
    }
  }

  .bi-notice-modal-expanded {
    left: 240px !important;
    width: calc(100% - 240px) !important;

    .el-drawer.ltr {
      left: 0 !important;
    }
  }

  .firefly-dialog .el-dialog__header {
    border-bottom: none !important;
    padding-bottom: 0 !important;
  }

  .bi-notice-message-auto-dialog {
    min-width: 420px !important;
    width: fit-content !important;
    max-width: 830px !important;
  }
</style>
