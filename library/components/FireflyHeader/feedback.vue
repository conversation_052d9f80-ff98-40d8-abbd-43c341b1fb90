<script lang="ts" setup>
  import { useSettingsStore } from '@/store/modules/settings'
  import { UploadServer } from '@/api/setting'
  import { editFeedback, queryMyFeedback, delFeedback } from '@/api/index'
  import { TabPaneName } from 'element-plus'

  const $baseMessage: any = inject('$baseMessage')
  const $baseConfirm: any = inject('$baseConfirm')
  const settingsStore = useSettingsStore()
  const { theme } = storeToRefs(settingsStore)

  const props = defineProps({
    drawerVisible: {
      type: Boolean,
      default: undefined,
    },
  })
  const emit = defineEmits(['update:drawerVisible'])
  const visible = useVModel(props, 'drawerVisible', emit)

  const activeName = ref('feedback-add')

  const handleTabChange = (tabName: TabPaneName) => {
    if (tabName !== 'feedback-list') {
      return
    }
    if (feedbackList.value.length > 0) {
      return
    }
    getMyFeedback()
  }

  const form = reactive({ content: '' })
  const toolbarConfig = {
    toolbarKeys: [
      'bulletedList',
      'numberedList',
      'uploadImage',
      'insertLink',
      'codeBlock',
    ],
  }

  //提交反馈
  const btnLoading = ref(false)
  const editorRef: any = ref()
  const saveMyFeedback = () => {
    if (form.content === '') {
      return
    }

    $baseConfirm('确认是否提交反馈', null, async () => {
      btnLoading.value = true
      await editFeedback(form)

      $baseMessage('操作成功', 'success', 'vab-hey-message-success')
      // drawerVisible.value = false
      btnLoading.value = false
      editorRef.value.clear()
      getMyFeedback()
    })
  }

  //获取反馈
  const listLoading = ref(false)
  const feedbackList: any = ref([])
  const getMyFeedback = async () => {
    page.value = 1
    listLoading.value = true
    const {
      data: { data },
    } = await queryMyFeedback({ limit: 5, page: page.value })
    if (data.length === 0) {
      noMoreData.value = true
    } else {
      noMoreData.value = false
    }
    feedbackList.value = data
    listLoading.value = false
  }

  //删除
  const showVisibleId = ref(0)
  const confirmDel = async (info: any, index: number) => {
    await delFeedback({ ids: info.id })

    $baseMessage('操作成功', 'success', 'vab-hey-message-success')

    feedbackList.value.splice(index, 1)
  }

  //加载更多
  const moreLoading = ref(false)
  const page = ref(1)
  const noMoreData = ref(false)
  const loadMore = async () => {
    page.value++
    const {
      data: { data },
    } = await queryMyFeedback({ limit: 5, page: page.value })
    if (data.length === 0) {
      noMoreData.value = true
    } else {
      noMoreData.value = false
    }
    feedbackList.value.push(...data)
    moreLoading.value = false
  }
</script>

<template>
  <div>
    <el-drawer
      v-model="visible"
      direction="rtl"
      @close="$emit('update:drawerVisible', false)"
      append-to-body
    >
      <template #header>
        <span class="text">意见反馈</span>
      </template>

      <el-tabs v-model="activeName" @tab-change="handleTabChange">
        <el-tab-pane label="我要反馈" name="feedback-add">
          <div class="feedback-form">
            <VabEditor
              ref="editorRef"
              :upload-url="UploadServer"
              v-model:value="form.content"
              :toolbar-config="toolbarConfig"
              :auto-height="false"
              :height="300"
              placeholder="您可以反馈数字平台使用问题，或者对公司的任何建议"
            />
          </div>
        </el-tab-pane>
        <el-tab-pane label="反馈记录" name="feedback-list">
          <div class="feedback-list" v-loading="listLoading">
            <div
              v-for="(item, index) in feedbackList"
              :key="item.id"
              class="feedback-item"
            >
              <div v-html="item.content" class="content"></div>

              <el-popover
                :visible="showVisibleId === item.id"
                placement="top"
                :width="160"
              >
                <p>你确定要删除该反馈吗</p>
                <div style="margin: 0; text-align: right">
                  <el-button size="small" text @click="showVisibleId = 0">
                    取消
                  </el-button>
                  <el-button
                    size="small"
                    type="primary"
                    @click="confirmDel(item, index)"
                  >
                    确定
                  </el-button>
                </div>
                <template #reference>
                  <el-button
                    class="del-btn"
                    type="danger"
                    size="small"
                    @click="showVisibleId = item.id"
                  >
                    删除
                  </el-button>
                </template>
              </el-popover>
            </div>

            <el-divider v-if="noMoreData" style="margin-top: 20px">
              没有更多了
            </el-divider>

            <div v-if="!noMoreData" class="load-more-box">
              <el-button :loading="moreLoading" @click="loadMore">
                加载更多
              </el-button>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>

      <template v-if="activeName === 'feedback-add'" #footer>
        <div style="text-align: center">
          <el-button
            :loading="btnLoading"
            type="primary"
            size="large"
            @click="saveMyFeedback"
          >
            提交反馈
          </el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<style lang="scss" scoped>
  :deep(.el-drawer__header) {
    margin-bottom: 0px;
  }

  :deep(.el-tabs__active-bar) {
    left: 20px; //这个只能是写死，暂无更优雅的居中办法
    width: 20px !important;
  }

  :deep(.el-drawer__body) {
    padding-top: 10px;
  }

  :deep(.el-tabs__header) {
    margin-bottom: 0px;
  }
  .refresh-btn {
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  .feedback-form {
    margin-top: 20px;
  }

  .feedback-list {
    .feedback-item {
      position: relative;
      padding: 20px;
      margin-top: 10px;
      color: #000;
      border-radius: 6px;
      // border-bottom: 1px solid #ddd;

      .content {
        :deep(p) {
          padding: 0px;
          margin: 0px;
        }
      }

      .del-btn {
        position: absolute;
        right: 15px;
        bottom: 15px;
        visibility: hidden;
      }

      &:hover {
        background: #f4f4f4;
        .del-btn {
          visibility: visible;
        }
      }
    }
  }

  .load-more-box {
    margin-top: 30px;
    text-align: center;
  }
</style>
