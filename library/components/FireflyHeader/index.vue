<script lang="ts" setup>
  import { useRoutesStore } from '@/store/modules/routes'

  import Feedback from './feedback.vue'

  defineProps({
    layout: {
      type: String,
      default: 'horizontal',
    },
  })

  const routesStore = useRoutesStore()
</script>

<template>
  <div class="vab-header">
    <div class="vab-main">
      <el-row :gutter="20">
        <el-col :span="3">
          <firefly-logo />
        </el-col>

        <el-col :span="10">
          <firefly-search />
        </el-col>

        <el-col :span="11">
          <div class="right-panel">
            <!-- <vab-notice /> -->

            <!-- <el-divider direction="vertical" class="divider" /> -->

            <Feedback />

            <el-divider direction="vertical" class="divider" />
            <vab-refresh />
            <el-divider direction="vertical" class="divider" />
            <vab-avatar />
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  @use 'sass:math';
  .divider {
    margin: 0 20px;
    border-color: #298cf7;
  }

  $base-menu-height: 40px;
  .vab-header {
    display: flex;
    align-items: center;
    justify-items: flex-end;
    height: $base-header-height;
    background: $base-menu-background;

    .vab-main {
      padding: 0 $base-padding 0 $base-padding;

      .right-panel {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: $base-header-height;
        color: #fff;

        :deep(i) {
          ///里面的图标距离文字均为3
          margin-right: 3px;
        }
      }
    }
  }
</style>
