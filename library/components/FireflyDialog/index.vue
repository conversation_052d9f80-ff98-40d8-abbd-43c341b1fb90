<script lang="ts" setup>
  const props = defineProps({
    alignCenter: {
      type: Boolean,
      default: true,
    },
    appendToBody: {
      type: Boolean,
      default: false,
    },
    lockScroll: {
      type: Boolean,
      default: false,
    },
    width: {
      type: [String, Number],
      default: '50%',
    },
    height: {
      //body高度，不包含header和footer，当height<maxHeight时，height属性将失效
      //如果需设置loading效果，最好是设置height，否则将出现高度由低到maxheight的闪烁过程
      type: [String, Number],
      default: 'auto',
    },
    minHeight: {
      //body最大高度，不包含header和footer,
      type: [String, Number],
      default: '1px',
    },
    maxHeight: {
      //body最大高度，不包含header和footer,
      type: [String, Number],
      default: '100%',
    },
    maxWidth: {
      //body最大高度，不包含header和footer,
      type: [String, Number],
      default: '100%',
    },
    overflowY: {
      type: String,
      default: 'scroll',
    },
    overflowX: {
      type: String,
      default: 'clip',
    },
    modelValue: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: null,
    },
    showClose: {
      type: Boolean,
      default: true,
    },
    showFullscreen: {
      type: Boolean,
      default: false,
    },
    draggable: {
      //拖曳会有卡顿，官网element不会，原因未知
      type: Boolean,
      default: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    closeOnClickModal: {
      type: Boolean,
      default: false,
    },

    //是否显示默认按钮
    showDefaultButton: {
      type: Boolean,
      default: false,
    },
    confirmText: {
      type: String,
      default: '确认',
    },
    confirmBtnDisabled: {
      //disabled
      type: Boolean,
      default: false,
    },
    cancelText: {
      type: String,
      default: '取消',
    },
    closeTips: {
      type: Boolean,
      default: false,
    },
    destroyOnClose: {
      //是否关闭后销毁
      type: Boolean,
      default: true,
    },
    closeOnPressEscape: {
      //是否按esc关闭dialog
      type: Boolean,
      default: false,
    },
    neverClose: {
      // 用以保持dialog常驻
      type: Boolean,
      default: false,
    },
    footerClass: {
      // dialog底下 按钮处样式 foot-btn-bar-small-padding
      type: String,
      default: '',
    },
  })
  const emit = defineEmits(['update:modelValue', 'confirm', 'close'])

  const dialogVisible = useVModel(props, 'modelValue', emit)
  const isFullscreen = ref(false)

  const closeDialog = async () => {
    if (props.closeTips) {
      // 由于baseConfirm不能控制ESC键，按下ESC键时会重新触发，导致baseConfirm无法正常显示，所以在于用el原生的
      ElMessageBox.confirm('确认关闭窗口吗?', '温馨提示', {
        closeOnPressEscape: false,
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        customClass: 'custom-confirm',
      })
        .then(() => {
          dialogVisible.value = false
          emit('close')
        })
        .catch(() => {})
      // await $baseConfirm('确认关闭窗口吗？', null, async () => {
      // dialogVisible.value = false
      // })
    } else {
      dialogVisible.value = props.neverClose ?? false
      emit('close')
    }
  }

  const setFullscreen = () => {
    isFullscreen.value = !isFullscreen.value
  }
  const handleConfirm = () => {
    emit('confirm')
  }

  //判断是否有footer元素传递进来
  const slotsfooter = !!useSlots().footer
  const hasFooter = ref(slotsfooter)
  //判断是否有header元素传递进来
  const slotsheader = !!useSlots().header
  const hasHeader = ref(slotsheader)
  const iconSize = 20

  const hideTitle = computed(() => {
    if (hasHeader.value || props.title) {
      return false
    }
    return true
  })
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    v-bind="$attrs"
    :draggable="draggable"
    :lock-scroll="lockScroll"
    :show-close="false"
    :destroy-on-close="destroyOnClose"
    :close-on-click-modal="closeOnClickModal"
    :close-on-press-escape="closeOnPressEscape"
    :width="width"
    :align-center="alignCenter"
    :append-to-body="appendToBody"
    :class="[hideTitle ? 'hide-title-dialog' : '', 'firefly-dialog']"
    :style="{ maxWidth: maxWidth }"
  >
    <template #header>
      <div v-if="hasHeader">
        <slot name="header"></slot>
      </div>
      <div v-else-if="title">
        <slot name="header">
          <span class="el-dialog__title">{{ title }}</span>
        </slot>
      </div>
      <div v-if="showClose" class="close-btn">
        <button aria-label="close" type="button" @click="closeDialog">
          <vab-icon
            icon="close-line"
            :style="{
              fontSize: iconSize + 'px',
            }"
          />
        </button>
      </div>
    </template>
    <div
      v-loading="loading"
      class="vab-dialog-body"
      :style="{
        height: height,
        minHeight: minHeight,
        maxHeight: maxHeight,
        overflowY: overflowY,
        overflowX: overflowX,
      }"
    >
      <slot></slot>
    </div>
    <template #footer>
      <div
        v-if="hasFooter && !showDefaultButton"
        :class="props.footerClass != '' ? props.footerClass : 'foot-btn-bar'"
      >
        <slot name="footer"></slot>
      </div>

      <div
        v-else-if="showDefaultButton"
        :class="props.footerClass != '' ? props.footerClass : 'foot-btn-bar'"
      >
        <el-button @click="closeDialog">
          {{ cancelText }}
        </el-button>
        <el-button
          :disabled="confirmBtnDisabled"
          :loading="loading"
          type="primary"
          @click="handleConfirm"
        >
          {{ confirmText }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style lang="scss">
  //当append-to-body为true时，这边样式不生效，所以需要设置为全局
  .firefly-dialog {
    &.hide-title-dialog {
      & > .el-dialog__header {
        //>防止多层dialog混淆
        height: 0px;
        padding: 0;
      }
    }

    border-radius: 10px;
    .el-dialog__header {
      position: relative;
      // display: flex;
      // align-items: center;
      // justify-content: space-between;
      padding: 20px 20px 0px 20px;
      margin: 0px;
    }

    .close-btn {
      position: absolute;
      top: var(--el-dialog-padding-primary);
      right: var(--el-dialog-padding-primary);
      z-index: 10;
      button {
        padding: 0;
        margin-left: 15px;
        font-size: var(--el-message-close-size, 16px);
        color: var(--el-color-info);
        cursor: pointer;
        background: transparent;
        border: none;
        outline: none;
        // transition: $base-transition;
        &:hover i {
          color: var(--el-color-primary);
        }
      }
    }

    .el-dialog__title {
      font-size: 16px;
      font-weight: bold;
      color: #333;
    }
    .el-dialog__body {
      // padding: 0px;
      padding: 12px 20px;
    }
    .el-dialog__footer {
      padding: 0px; //避免产生高度
    }

    .foot-btn-bar {
      padding: 20px;
      // border-top: 1px solid #dcdfe6;
      // box-shadow: 0px 0px 4px rgb(0 0 0 / 10%);
    }
    .foot-btn-bar-small-padding {
      padding: 0 24px 24px 24px;
    }

    &.is-fullscreen {
      top: 0px !important;
      left: 0px !important;
      display: flex;
      flex-direction: column;

      .el-dialog__body {
        flex: 1;
        overflow: auto;
      }

      .el-dialog__footer {
        padding-bottom: 10px;
        border-top: 1px solid var(--el-border-color-base);
      }
    }
  }
</style>
