<script lang="ts" setup>
  import { VabRoute } from '/#/router'
  import { isExternal } from '@/utils/validate'
  import variables from '@vab/styles/variables/vab-custom-variables.module.scss'
  import { useRoutesStore } from '@/store/modules/routes'
  import { defaultOpeneds, openFirstMenu, uniqueOpened } from '@/config'
  import { useSettingsStore } from '@/store/modules/settings'
  import { TabsPaneContext } from 'element-plus'
  import { IconType } from '~/src/icon'
  import CommonIcon from '~/src/components/CommonIcon.vue'
  import Feedback from '../FireflyHeader/feedback.vue'
  import Message from '../FireflyHeader/message.vue'
  import Search from '../../../src/views/index/components/Search.vue'
  import { getUserUnreadNoticeCount } from '@/api/userNoticePush'

  const route: VabRoute = useRoute()
  const router = useRouter()

  const settingsStore = useSettingsStore()
  const { theme, collapse } = storeToRefs(settingsStore)
  const routesStore = useRoutesStore()

  const title = ref(route.matched[0].meta.title)

  const {
    getTab: tab,
    getTabMenu: tabMenu,
    getActiveMenu: activeMenu,
    getRoutes: routes,
    getPartialRoutes: partialRoutes,
  }: any = storeToRefs(routesStore)

  const handleTabClick = (ctx: TabsPaneContext) => {
    let i = Number(ctx.index)
    title.value = routes.value[i].meta.title
    nextTick(() => {
      if (isExternal(tabMenu.value.path)) {
        window.open(tabMenu.value.path)
        setTimeout(() => {
          router.push('/')
        }, 1000)
      } else if (openFirstMenu) {
        router.push(tabMenu.value.redirect || tabMenu.value)
      }
    })
  }
  ///切换菜单栏
  const { toggleCollapse } = settingsStore

  //是否隐藏二级菜单
  const menuWidth = computed(() => {
    if (collapse.value) {
      return 72
    } else {
      return 240
    }
  })

  const handleRoutes = computed(() => {
    return routes.value.flatMap((route: any) =>
      route.meta.levelHidden && route.children ? [...route.children] : route
    )
  })

  const feedbackShow = ref(false)
  const messageShow = ref(false) // 消息通知框是否显示

  // 添加未读消息数量和消息数据的响应式变量
  const unreadCount = ref(0)

  //底部菜单
  const btMenu = computed(() => {
    return [
      {
        icon: IconType.Messages,
        label: '消息',
        hintText:
          unreadCount.value > 0
            ? unreadCount.value > 99
              ? '99+'
              : unreadCount.value.toString()
            : '',
        onClick: () => {
          messageShow.value = true
          getUnreadCount() // 打开弹窗的时候获取未读数量
        },
      },
      {
        icon: IconType.Feedback,
        label: '反馈',
        hintText: '',
        onClick: () => {
          feedbackShow.value = true
        },
      },
      {
        icon: IconType.Collapse,
        label: collapse.value ? '展开' : '收起导航',
        onClick: () => {
          toggleCollapse()
        },
      },
    ]
  })

  const handleToPage = (item: any) => {
    // console.log(item.path)
    router.push(
      item.children.length > 0 && item.childrenPathList[0].indexOf('index') > -1
        ? item.childrenPathList[0]
        : item.path
    )
  }

  const getUnreadCount = async () => {
    try {
      const response = await getUserUnreadNoticeCount()
      unreadCount.value = response.data || 0
    } catch (error) {
      console.error('获取未读消息数量失败:', error)
      unreadCount.value = 0
    }
  }

  // 处理消息读取事件 - 当Message组件标记消息为已读时
  const handleMessageRead = (eventData: any) => {
    if (eventData && eventData.isMarkAll) {
      unreadCount.value = unreadCount.value - eventData.count
    } else {
      // 如果是单个消息，则计数减1
      if (unreadCount.value > 0) {
        unreadCount.value -= 1
      }
    }
  }

  // 处理消息弹窗关闭时刷新未读数量
  const handleMessageDrawerClose = () => {
    getUnreadCount() // 重新获取未读数量，确保数据同步
  }

  // 在组件挂载时获取消息数据
  onMounted(() => {
    getUnreadCount()
  })
</script>

<template>
  <el-scrollbar
    class="vab-column-bar-container"
    :class="{
      ['vab-column-bar-container-' + theme.columnStyle]: true,
    }"
    :style="{
      width: menuWidth + 'px',
    }"
  >
    <div class="left-menu-box">
      <firefly-logo />
      <div style="margin-bottom: 10px">
        <Search />
      </div>

      <!--菜单收缩情况-->
      <div v-if="collapse" class="collapse-menu-box">
        <el-menu
          :collapse="collapse"
          :collapse-transition="false"
          :default-active="activeMenu.data"
          :default-openeds="defaultOpeneds"
          menu-trigger="click"
          mode="vertical"
          :unique-opened="uniqueOpened"
          :active-text-color="variables['menu-color-active']"
          :text-color="variables['menu-color']"
          class="collapse-menu"
        >
          <template
            v-for="(item, index) in handleRoutes"
            :key="index + item.name"
          >
            <firefly-menu v-if="!item.meta.hidden" :item="item" />
          </template>
        </el-menu>

        <div class="bt-menu-box">
          <div
            v-for="item in btMenu"
            :key="item.label"
            @click="item.onClick"
            class="bt-menu-item"
          >
            <common-icon :type="item.icon" :size="24" />

            <div class="label">{{ item.label }}</div>
            <el-badge
              v-if="item.hintText"
              :value="item.hintText"
              class="abs-dot"
            />
          </div>
        </div>
      </div>

      <!--菜单展开情况-->
      <div v-else class="expanded-menu-box">
        <el-menu
          :collapse="collapse"
          :collapse-transition="false"
          :default-active="activeMenu.data"
          :default-openeds="defaultOpeneds"
          menu-trigger="click"
          mode="vertical"
          background-color="transparent"
          :unique-opened="true"
          class="custom-menu"
        >
          <template
            v-for="(item, index) in handleRoutes"
            :key="index + item.name"
          >
            <vab-menu v-if="!item.meta.hidden" :item="item" />
          </template>
        </el-menu>
        <div class="bt-menu-box">
          <div
            v-for="item in btMenu"
            :key="item.label"
            @click="item.onClick"
            class="bt-menu-item"
          >
            <common-icon :type="item.icon" :size="24" />

            <div class="label">{{ item.label }}</div>

            <span class="num-badge" v-if="item.hintText">
              {{ item.hintText }}
            </span>
          </div>
        </div>
      </div>

      <vab-avatar style="margin-top: auto" />

      <Feedback v-model:drawerVisible="feedbackShow" />

      <!-- 消息通知box -->
      <Message
        v-model:drawerVisible="messageShow"
        @message-read="handleMessageRead"
        @close="handleMessageDrawerClose"
      />

      <!--收起展开按钮-->
      <!-- <div class="toggle-menu-btn-box">
        <div class="toggle-menu-btn" @click="toggleCollapse">
          <vab-icon
            class="fold-unfold"
            :icon="collapse ? 'menu-unfold-line' : 'menu-fold-line'"
          />
          <span v-if="!collapse">收起</span>
        </div>
      </div> -->
    </div>
  </el-scrollbar>
</template>

<style lang="scss" scoped>
  @use 'sass:math';

  .vab-column-bar-container {
    position: relative;
    display: flex;
    height: 100vh;
    overflow: hidden;
    background-color: #f3f4f4;
    border-right: 1px solid #dcdfe6;
    box-shadow: $base-box-shadow;

    :deep() {
      --el-menu-base-level-padding: 12px;
      --el-menu-level-padding: 26px; //调整菜单距离左侧距离
      .el-scroll {
        height: auto;
      }
      * {
        transition: $base-transition;
      }

      .el-scrollbar__view {
        width: 100%;
        height: 100%;
        background-color: #ecf2ff;
      }

      .el-menu {
        flex-grow: 1;
      }
      .el-sub-menu__title:hover {
        background-color: $base-color-hover !important;
      }

      .el-tabs {
        width: $base-left-menu-width-min;
        box-shadow: $base-box-shadow;
      }
    }

    &-card {
      :deep() {
        .el-tabs {
          .el-tabs__header {
            float: none;
          }

          .el-tabs__item {
            display: block;
            padding: 5px !important;

            .vab-column-grid {
              height: 40px;
              border-radius: 5px;

              &:hover {
                color: #fff !important;
                background-color: $base-color-primary !important;

                i,
                svg {
                  color: #fff !important;
                }
              }

              &.is-active {
                color: var(--el-color-primary);
              }
            }

            &.is-active {
              .vab-column-grid {
                font-weight: 700;
                color: var(--el-color-primary);
              }
            }
          }
        }

        .el-sub-menu .el-sub-menu__title,
        .el-menu-item {
          border-radius: 5px;
        }

        // .el-menu--inline {
        //   // margin-left: 24px;
        // }
      }
    }

    .vab-column-grid {
      display: flex;
      align-items: center;
      overflow: hidden;
      color: #7e899d;
      text-align: center;
      text-overflow: ellipsis;
      word-break: break-all;
      white-space: nowrap;

      &.is &-vertical,
      &-card,
      &-arrow {
        justify-content: center;

        > div {
          svg {
            position: relative;
            top: 8px;
            display: block;
            width: $base-font-size-default + 4;
            height: $base-font-size-default + 4;
          }

          [class*='ri-'] {
            display: block;
            height: 20px;
          }
        }
      }
    }

    :deep(.el-scrollbar__wrap) {
      width: 100%;
    }

    :deep(.expanded-menu-box) {
      & > .el-menu {
        margin: 0 12px;
      }
      .el-menu {
        width: auto;
        & > li {
          width: 100%;
          margin: 8px 0px;
        }
        border: 0;
        .el-divider {
          margin: 0 0 $base-margin 0;
          background-color: #f6f6f6;

          &__text {
            color: var(--el-color-black);
          }
        }

        .el-menu-item,
        .el-sub-menu__title {
          height: $base-menu-item-height;

          overflow: hidden;
          line-height: $base-menu-item-height;
          text-overflow: ellipsis;
          white-space: nowrap;
          vertical-align: middle;
        }
      }
    }
  }

  .vab-column-bar-right-menu {
    flex-grow: 1;
    padding: 12px 8px;
    background: #fff;
    &.is-collapse {
      //收缩时，隐藏右侧菜单
      display: none;
    }
  }
  .first-menu-title {
    //一级菜单标题
    margin-left: 10px;
  }

  .right-menu-title {
    padding-left: 13px;
    //右侧菜单顶部小标题
    margin-top: 10px;
    margin-bottom: 18px;
    font-size: 16px;
    font-weight: 800;
  }

  .toggle-menu-btn-box {
    position: absolute;
    bottom: 50px;
    z-index: 999;
    width: 100%;
  }

  .toggle-menu-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: calc(100% - 20px);
    padding: 10px;
    margin: 0px auto;
    cursor: pointer;
    border-radius: 5px;
    & > span {
      margin-left: 10px;
    }
    &:hover {
      color: #fff;
      background-color: $base-color-primary;
    }
  }

  .left-menu-box {
    position: relative;
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
    background-color: #f3f4f4;
    //必需
    :deep {
      .el-tabs__nav {
        background: none !important;
      }
      .el-tabs__nav-wrap {
        background: none !important;
      }
    }
  }

  //收缩菜单
  .collapse-ul {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0px 3px;
  }

  .collapse-menu-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 90%;
    // width: 42px;
    height: 55px;
    margin: 8px auto 8px auto;
    font-size: 14px;
    color: $base-menu-color;
    text-align: center;
    cursor: pointer;
    border-radius: 6px;
    &:hover {
      background: $base-color-hover;
    }
    &.is-active {
      color: $base-color-primary !important;
      background-color: $base-color-hover;

      & .collapse-menu-title {
        font-weight: 700;
        color: $base-color-primary !important;
      }
    }
  }

  .collapse-menu-icon {
    width: 24px;
    height: 24px;
  }
  .collapse-menu-title {
    color: $base-menu-color;
  }

  .collapse-menu-box {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: auto;
    height: calc(100% - 200px);
    overflow-x: hidden;
    overflow-y: auto;

    & > .collapse-menu {
      background: #f3f4f4;
    }

    .el-menu-item,
    .el-sub-menu .el-sub-menu__title {
      flex-direction: column;
      padding: 0px;

      & > span {
        width: auto;
        height: auto;
      }
    }
    :deep(.el-sub-menu) {
      flex-direction: column;
      padding: 0px;

      & > span {
        width: auto;
        height: auto;
      }
    }

    .bt-menu-box {
      .bt-menu-item {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: space-evenly;
        width: 90%;
        height: 55px;
        margin: 8px auto;
        font-size: 14px;
        color: $base-menu-color;

        cursor: pointer;
        border-radius: 4px;
        &:hover {
          background-color: $base-color-hover;
        }

        .abs-dot {
          position: absolute;
          top: 5px;
          right: 5px;
          font-size: 12px;
        }
      }
    }
  }

  .expanded-menu-box {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: calc(100% - 200px);

    .bt-menu-box {
      padding: 0px 12px;
      .bt-menu-item {
        display: flex;
        align-items: center;
        height: 40px;
        padding: 0 12px;
        margin-bottom: 8px;
        font-size: 14px;
        color: $base-menu-color;
        cursor: pointer;
        border-radius: 4px;
        &:hover {
          background-color: $base-color-hover;
        }

        .label {
          margin-left: 5px;
        }
      }
    }
  }

  .num-badge {
    min-width: 20px;
    height: 20px;
    padding: 2px 6px 0;
    margin-top: 0px;
    margin-left: auto;
    color: #fff;
    text-align: center;
    background-color: #ff5e4b;
    border-radius: 10px;
    font-size: 12px;
    line-height: 16px;
    white-space: nowrap;
    display: flex;
    justify-content: center;
    align-items: center;
  }
</style>
