<script lang="ts" setup>
  defineProps({
    itemOrMenu: {
      type: Object,
      default() {
        return null
      },
    },
  })
</script>

<template>
  <el-sub-menu :index="itemOrMenu.path" popper-class="collapse-item">
    <template #title>
      <vab-icon
        v-if="itemOrMenu.meta.icon"
        :icon="itemOrMenu.meta.icon"
        :is-custom-svg="true"
        :title="itemOrMenu.meta.title"
        class="custom-size"
      />
      <span :title="itemOrMenu.meta.title" class="title">
        {{ itemOrMenu.sd_name || itemOrMenu.title }}
      </span>
    </template>
    <slot />
  </el-sub-menu>
</template>
