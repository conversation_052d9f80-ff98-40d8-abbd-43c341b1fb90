<template>
  <component :is="menuComponent" v-if="!item.meta.hidden" :item-or-menu="item">
    <template v-if="item.children && item.children.length">
      <firefly-menu
        v-for="route in item.children"
        :key="route.path"
        :item="route"
      />
    </template>
  </component>
</template>

<script lang="ts">
  /* 防止偶发性自动导入失败 */
  import { computed, defineComponent } from 'vue'
  import { storeToRefs } from 'pinia'
  import { useSettingsStore } from '@/store/modules/settings'

  const imports = require.context('./components', true, /\.vue$/)
  const Components: {
    [key: string]: any
  } = {}
  imports.keys().forEach((key) => {
    Components[key.replace(/(\/|\.|vue)/g, '')] = imports(key).default
  })

  export default defineComponent({
    name: 'FireflyMenu',
    components: Components,
    props: {
      item: {
        type: Object,
        required: true,
      },
      layout: {
        type: String,
        default: '',
      },
    },
    setup(props) {
      //显示的菜单信息
      const settingsStore = useSettingsStore()
      const { collapse } = storeToRefs(settingsStore)

      ///子级菜单显示数量
      let childMenuNum = computed(() => {
        let res = props.item.children ? props.item.children.length : 0
        const hasChild =
          props.item.children &&
          props.item.children.some((_route: any) => {
            return _route.meta.hidden !== true
          }) //是否有需显示的子级菜单

        if (hasChild) {
          res = 0
          props.item.children.forEach((_route: any) => {
            if (_route.meta.hidden !== true) {
              res++
            }
          })
          return res
        }
        return res
      })

      const menuComponent = computed(() =>
        props.item.children &&
        props.item.children.some((_route: any) => {
          return _route.meta.hidden !== true
        })
          ? 'FireflySubMenu'
          : 'FireflyMenuItem'
      )

      return {
        collapse,
        menuComponent,
      }
    },
  })
</script>

<style lang="scss" scoped>
  :deep(.vab-icon) {
    width: 24px;
    height: 24px;
    margin-right: 0px;
  }

  .vab-menu-children-height {
    height: 60vh !important;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 0;
      height: 0;
    }
  }
</style>

<!--由于element-plus
bug使用popper-append-to-body=false会导致多级路由无法显示，故所有菜单必须生成至body下，样式必须放到body下-->
<style lang="scss">
  .el-popper.is-light {
    border: 0 !important;
  }

  .collapse-menu {
    & > .el-menu-item,
    & > .el-sub-menu > .el-sub-menu__title {
      font-size: 14px;
      &:hover {
        background-color: $base-color-hover;
      }
    }
  }
  .collapse-item .el-sub-menu__icon-arrow {
    color: #666 !important;
  }

  .collapse-item .el-sub-menu.is-active {
    font-weight: 800;
    color: $base-color-primary !important;
    background-color: $base-color-hover !important;
    .el-sub-menu__title {
      color: $base-color-primary !important;
      background-color: $base-color-hover !important;
    }
  }
  .collapse-item > ul > li {
    margin: 8px 10px;

    border-radius: 6px;

    .el-sub-menu__title {
      background: #fff !important;
    }
    span {
      color: #333;
    }
  }

  .collapse-item.is-active {
    background-color: $base-color-hover !important;
    span {
      font-weight: 800;
      color: $base-color-primary;
    }
  }
  .collapse-item > ul {
    border: 1px solid #f2f4f6;
    border-radius: 6px;
  }

  .collapse-item > ul > li:hover {
    background-color: #eaf1ff !important; //这个暂不能使用rgba，会导致重叠，存在二级菜单时颜色略深

    & > .el-sub-menu__title {
      background-color: #eaf1ff !important;
    }
  }

  .collapse-item > ul > li > div {
    border-radius: 6px;
  }

  .collapse-menu {
    width: 100%;
    span.title {
      width: auto !important;
      height: auto !important;
      line-height: 24px;
      visibility: inherit !important;
    }

    .vab-icon {
      margin: 0px !important;
    }

    .el-sub-menu__title,
    .el-menu-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 56px;
      height: 56px;
      padding: 0;
      margin: 8px auto;
    }

    .el-sub-menu.is-active > .el-sub-menu__title {
      font-weight: 800;
      color: $base-color-primary !important;
      background-color: $base-color-hover;
    }
  }
</style>
