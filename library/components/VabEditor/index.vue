<template>
  <div class="editor-container" :class="[!disabled && 'boder-box']">
    <Toolbar
      v-show="disabled === false && toolbarShow"
      ref="toolBar"
      :default-config="toolbarConfig"
      :editor="editorRef"
      :mode="mode"
      style="border-bottom: 1px solid #efefef"
      @click="clickTool"
    />

    <Editor
      v-model="valueHtml"
      :default-config="editorConfig"
      :mode="mode"
      :style="`height: ${customHeight}; min-height: ${minHeight}px;`"
      @custom-alert="customAlert"
      @on-blur="handleBlur"
      @on-change="handleChange"
      @on-created="handleCreated"
      @on-focus="handleFocus"
    />

    <div
      v-if="autoHeight && textOver && disabled && useShowMore"
      class="collapse-box"
      :class="[isHide && 'mask']"
    >
      <span v-if="isHide" class="show-more" @click="showMore">
        展开描述
        <vab-icon icon="arrow-down-s-line" />
      </span>
      <span v-else class="show-more" @click="hideMore">
        收起描述
        <vab-icon icon="arrow-up-s-line" />
      </span>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import '@wangeditor/editor/dist/css/style.css'
  import { useUserStore } from '@/store/modules/user'
  import { onBeforeUnmount, ref, shallowRef } from 'vue'
  import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
  import { ElMessage } from 'element-plus'
  import { Boot, IDomEditor } from '@wangeditor/editor'
  import markdownModule from '@wangeditor/plugin-md'
  import mentionModule, { MentionElement } from '@wangeditor/plugin-mention'
  import { marked } from 'marked'
  Boot.registerModule(markdownModule)
  Boot.registerModule(mentionModule)
  type InsertFnType = (url: string, alt: string, href: string) => void

  // 编辑器实例，必须用 shallowRef，重要！
  const editorRef = shallowRef()
  const $pub: any = inject('$pub')

  const props = defineProps({
    disabled: {
      type: Boolean,
      default: false,
    },
    value: {
      type: String,
      default: '',
    },
    mode: {
      type: String,
      default: 'default',
    },
    minHeight: {
      type: Number,
      default: 200,
    },
    height: {
      type: Number,
      default: 300,
    },
    toolbarShow: {
      type: Boolean,
      default: true,
    },
    autoHeight: {
      type: Boolean,
      default: true,
    },
    placeholder: {
      type: String,
      default: '尚未输入内容',
    },
    useShowMore: {
      type: Boolean,
      default: false,
    },
    //工具栏设置
    toolbarConfig: {
      type: Object,
      default: () => {
        return {
          excludeKeys: [
            'underline',
            'italic',
            'fontFamily',
            'lineHeight',
            'todo',
            'emotion',
            'group-indent',
            'undo',
            'redo',
            'fontSize',
            'divider',
            'group-video',
          ],
        }
      },
    },

    //图片上传地址不同场景不一致
    uploadUrl: {
      type: String,
      default: process.env.VUE_APP_UPLOAD_SERVER,
    },
  })
  const { mode, value, disabled, height, toolbarShow, autoHeight } =
    toRefs(props)
  const customHeight = ref('')

  // 未编辑时第一次检查的高度
  const initEditHeight = ref(0)

  watch(disabled, (v) => {
    const editor = editorRef.value
    container = editor.getEditableContainer().querySelector('.w-e-scroll')
    // 查看状态时，隐藏滚动条
    if (v === true) {
      container.style.setProperty('overflow-y', 'hidden', 'important')
      container.style.setProperty('max-height', 'none', 'important')
    } else {
      container.style.setProperty('overflow-y', 'auto', 'important')
      // container.style.setProperty('max-height', '300px', 'important')
    }
    handleDisabled(v)
  })

  const handleDisabled = (v: boolean) => {
    const editor = editorRef.value
    if (!editor) return
    // 编辑状态时，固定高度使滚动条出现
    if (v === false) {
      customHeight.value = `${height.value}px`
      editor.enable()
    } else {
      // 查看状态
      hideMore()
      editor.disable()
    }
  }

  const valueHtml = ref(``)

  watch(value, (v) => {
    valueHtml.value = v
  })
  // 监听富文本值变化
  watch(valueHtml, (v) => {
    $pub('editor-value-edit', v)
    // 文本值变化之后重新计算第一次的高度
    initEditHeight.value = 0
  })
  const textOver = ref(false) //文本是否已超出编辑器高度
  const isHide = ref(true)
  // 内容 HTML
  const userStore = useUserStore()

  const { token } = userStore
  const toolbarConfig = reactive(props.toolbarConfig)

  const isFullScreen = ref(false)

  // 显示弹框
  function showModal(editor: IDomEditor) {
    // 获取光标位置，定位 modal
    const domSelection = document.getSelection()
    const domRange = domSelection?.getRangeAt(0)
    if (domRange == null) return
    const selectionRect = domRange.getBoundingClientRect()

    // 获取编辑区域 DOM 节点的位置，以辅助定位
    const containerRect = editor.getEditableContainer().getBoundingClientRect()

    // 显示 modal 弹框，并定位
    // PS：modal 需要自定义，如 <div> 或 Vue React 组件
    console.log('@')

    // 当触发某事件（如点击一个按钮）时，插入 mention 节点
    function insertMention() {
      const mentionNode: MentionElement = {
        type: 'mention', // 必须是 'mention'
        value: '张三', // 文本
        info: { x: 1, y: 2 }, // 其他信息，自定义
        children: [{ text: '' }], // 必须有一个空 text 作为 children
      }

      editor.restoreSelection() // 恢复选区
      editor.deleteBackward('character') // 删除 '@'
      editor.insertNode(mentionNode) // 插入 mention
      editor.move(1) // 移动光标
    }
  }

  // 隐藏弹框
  function hideModal(editor: IDomEditor) {
    // 隐藏 modal
  }

  const editorConfig = reactive({
    placeholder: props.placeholder,
    readOnly: disabled.value,
    scroll: false, // 默认隐藏滚动条
    MENU_CONF: {
      uploadImage: {
        server: props.uploadUrl, // 上传图片的接口地址
        timeout: 5 * 1000, // 5s
        fieldName: 'file',
        headers: { Authorization: `Bearer ${token}` },
        maxFileSize: 10 * 1024 * 1024, // 10M
        base64LimitSize: 5 * 1024, // 5kb 以下插入 base64
        onBeforeUpload(files: any) {
          return files // 返回哪些文件可以上传,return false 会阻止上传
        },
        // 自定义插入图片,res 即服务端的返回结果
        customInsert(res: any, insertFn: InsertFnType) {
          if (res.code !== 200) {
            ElMessage.error(`上传失败`)
            return
          }
          //返回字段显示因不同接口不一致，故只能如此判断
          const src = res.data.upload?.attachment.url || res.data.full_url
          const name =
            res.data.upload?.attachment.fieldName || res.data.filename
          insertFn(src, name, src)
          ElMessage.success(`上传成功`)
        },
      },
    },
    EXTEND_CONF: {
      mentionConfig: {
        showModal, // 必须
        hideModal, // 必须
      },
    },
  })

  //container 编辑器父元素，editorTextarea可输入区域
  let container: any = null
  let editorTextarea: any = null

  // 编辑器回调函数
  const handleCreated = (editor: IDomEditor) => {
    editorRef.value = editor // 记录 editor 实例，重要！
    initComponect()
    nextTick(() => {
      container = editor.getEditableContainer().querySelector('.w-e-scroll')
      editorTextarea = container?.querySelector('div')
      if (value.value) {
        editor.setHtml(value.value)
      }
      // let reg = /<[^>]+>/g //判断是否有html元素
      // if (reg.test(value.value)) {
      //   editor.setHtml(value.value)
      // } else {
      //   editor.setHtml(`<p>${value.value}</p>`)
      // }

      initContainer(editor)
      // if (autoHeight.value === true) {
      //   //假如是自动高度，也即显示“显示更多”按钮的情况
      //   // container.style.setProperty('overflow-y', 'hidden', 'important')
      // }
    })
  }

  //根据内容高度初始化显示编辑器
  const initContainer = (editor: IDomEditor) => {
    if (!container || !editorTextarea) {
      return
    }

    /*遍历查找img标签，若全部已加载完毕，进行高度计算，querySelectorAll获取的不是array*/
    const imgs = container.querySelectorAll('img')
    const imgloadPromise = []
    for (let img of imgs) {
      imgloadPromise.push(
        new Promise((resolve, reject) => {
          img.onload = function () {
            resolve(true)
          }
          img.onerror = function () {
            reject(true)
          }
        })
      )
    }
    Promise.all(imgloadPromise)
      .then(() => {
        setAutoMore()
        // if (!textOver.value && disabled.value) {
        //   //说明内容未超出高度,且是不可编辑状态
        //   customHeight.value = 'auto'
        // }
      })
      .catch(() => {
        setAutoMore()
      })
  }

  /**
   * 非编辑状态下显示更多
   */
  const showMore = () => {
    isHide.value = false
    customHeight.value = 'auto'
  }

  /**
   * 非编辑状态下隐藏更多
   */
  const hideMore = () => {
    // container.style.overflowY = 'hidden'
    isHide.value = true
    const id = editorTextarea.querySelector(':first-child').id
    if (id) {
      editorRef.value.scrollToElem(id)
    }
    customHeight.value = `${height.value}px`
    setAutoMore()
  }

  const clickTool = (e: any) => {
    // 监听全屏操作，改变编辑器的zindex，原因顶栏与屏闭层在同一容器下，编辑器的层无法两边兼容
    if (
      e.target.dataset.menuKey == 'fullScreen' ||
      e.target.parentNode.dataset.menuKey == 'fullScreen'
    ) {
      if (
        editorRef.value.getEditableContainer().getBoundingClientRect().width /
          window.innerWidth >=
          0.95 &&
        !isFullScreen.value
      ) {
        isFullScreen.value = true
        // 判断是否全屏
        emit('on-full', isFullScreen.value)
      } else {
        isFullScreen.value = false
        // 返回非全屏状态时存在迟时
        setTimeout(() => {
          emit('on-full', isFullScreen.value)
        }, 500)
      }
    }
  }

  // 事件传递
  const emit = defineEmits([
    'update:value',
    'custom-alert',
    'on-change',
    'on-blur',
    'custom-paste',
    'on-focus',
    'on-full',
  ])

  const customAlert = (info: any, type: any) => {
    emit('custom-alert', { type, info })
  }
  const handleChange = (editor: any) => {
    // emit('update:value', editor.getHtml())
    // 这里用update:value双向绑定，会有卡死bug
    const t = editor.getText().replace(/\s/g, '')

    emit('on-change', editor.getHtml())
    //放在handleBlur中，会有编辑完内容，直接点保存，内容未同步至父组件bug
    //因img标签用getText也会判定空字符串
    emit(
      'update:value',
      t !== '' || editor.getHtml().indexOf('img') > -1 ? editor.getHtml() : ''
    )
  }
  const handleFocus = (editor: any) => {
    emit('on-focus', editor)
  }
  const handleBlur = (editor: any) => {
    emit('on-blur', editor)
  }

  const insertText = (str: string) => {
    const editor = editorRef.value
    if (editor == null) return
    editor.insertText(str)
  }

  const disable = () => {
    const editor = editorRef.value
    if (editor == null) return
    editor.disable()
  }

  const focus = (flag?: boolean) => {
    const editor = editorRef.value
    editor.focus(flag)
  }

  //清空编辑器内容
  const clear = () => {
    valueHtml.value = ''
  }

  /**
   * 设置未编辑状态自动
   */
  const setAutoMore = () => {
    if (props.useShowMore == true) {
      const editHeight =
        initEditHeight.value > 0
          ? initEditHeight.value
          : editorTextarea.clientHeight
      if (initEditHeight.value == 0) {
        initEditHeight.value = editorTextarea.clientHeight
      }
      const boxHeight =
        container.clientHeight < 300 ? container.clientHeight : 300
      if (editHeight && boxHeight) {
        if (editHeight > boxHeight) {
          textOver.value = true
          customHeight.value = `${height.value}px`
        } else {
          textOver.value = false
          customHeight.value = 'auto'
        }
      }
    } else {
      if (autoHeight.value == true) {
        customHeight.value = `auto`
      } else {
        customHeight.value = `${height.value}px`
      }
    }
  }

  const initComponect = () => {
    const editor = editorRef.value
    container = editor.getEditableContainer().querySelector('.w-e-scroll')
    if (props.disabled == true) {
      customHeight.value = `${height.value}px`
      container.style.setProperty('overflow-y', 'hidden', 'important')
    } else {
      customHeight.value = `${height.value}px`

      container.style.setProperty('overflow-y', 'auto', 'important')
    }
  }

  onMounted(() => {})

  // 组件销毁时，也及时销毁编辑器，重要！
  onBeforeUnmount(() => {
    const editor = editorRef.value
    if (!editor) return
    editor.destroy()
  })

  //暴露供父组件调用
  defineExpose({
    insertText,
    disable,
    focus,
    clear,
  })
</script>

<style lang="scss">
  .w-e-full-screen-container {
    //修复点击全屏时，部分元素层级较高问题
    z-index: 999999 !important;
  }
  .editor-container {
    width: 100%;
    &.boder-box {
      border: 1px solid #efefef;
    }
    .w-e-scroll {
      min-height: 60px;
      & > div {
        min-height: 60px;
      }
    }
    .w-e-bar-item {
      padding: 0px;
    }
    .w-e-text-container [data-slate-editor] p {
      margin: 5px;
    }
    .w-e-text-placeholder {
      top: 0px;
      margin-left: 5px;
      font-style: normal;
      line-height: 32px;
    }

    .w-e-text-container [data-slate-editor] li {
      margin: 0px;
      line-height: 1.5;
    }
    .show-more {
      padding: 5px 15px;
      font-size: 14px;
      color: #1890ff;
      cursor: pointer;
      background: #fff;
      border-radius: 30px;
      box-shadow: 0px 3px 6px 1px rgba(0, 0, 0, 0.08);
    }

    .collapse-box {
      position: relative;
      padding: 10px 0px;
      text-align: center;
      &.mask::before {
        position: absolute;
        display: block;
        width: 100%;
        height: 100px;
        margin-top: -100px;
        content: '';
        background: linear-gradient(0deg, #fff 0%, rgba(255, 255, 255, 0) 100%);
      }
    }
  }
</style>
