import * as Diff2Html from 'diff2html'

/* 可以浏览的文件格式 */
export const fileExtensionsToPreview = [
  'pdf',
  'mp4',
  'mp3',
  'txt',
  'log',
  'doc',
  'docx',
  'xlsx',
  'xls',
]

/* office办公文件常见格式 */
export const fileExtensionsForOffice = [
  // 'pdf',
  'doc',
  'docx',
  'xlsx',
  'xls',
  'ppt',
  'pptx',
]

/* excel文件常见格式 */
export const fileExtensionsForExcel = ['xlsx', 'xls']

/* word文件常见格式 */
export const fileExtensionsForWord = ['doc', 'docx']

/* ppt文件常见格式 */
export const fileExtensionsForPpt = ['ppt', 'pptx']

/**
 * 查看patch文件
 * @param patchFiles
 */
export const loadDiff = async (patchFiles) => {
  try {
    // 使用 fetch 获取文件内容
    const response = await fetch(patchFiles.url)
    if (!response.ok) {
      throw new Error('Failed to fetch file')
    }

    const content = await response.text()
    // 创建 Blob 对象
    const blob = new Blob([content], { type: 'text/x-patch' })
    // 使用 FileReader 读取 Blob 对象
    const reader = new FileReader()
    reader.readAsText(blob)

    // 将 Diff2Html.html 的结果返回
    return Diff2Html.html(content, {
      drawFileList: false,
      matching: 'lines',
      // outputFormat: 'side-by-side',
    })
  } catch (error) {
    console.error('Error loading file:', error)
    return ''
  }
}

export const loadLog = async (patchFiles) => {
  try {
    // 使用 fetch 获取文件内容
    const response = await fetch(patchFiles.url)
    if (!response.ok) {
      throw new Error('Failed to fetch file')
    }

    const content = await response.text()
    // 创建 Blob 对象
    const blob = new Blob([content], { type: 'text/plain' })
    // 使用 FileReader 读取 Blob 对象
    const reader = new FileReader()
    reader.readAsText(blob)

    return content.replace(/[\r\n]/g, '<br>')
  } catch (error) {
    console.error('Error loading file:', error)
    return ''
  }
}
