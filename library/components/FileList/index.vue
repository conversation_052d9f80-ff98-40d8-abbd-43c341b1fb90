<template>
  <div class="file-list">
    <el-image-viewer
      v-if="state.showImage"
      @close="closeViewer"
      :initial-index="imgIndex"
      :url-list="srcList"
    />
    <firefly-dialog
      title="预览"
      v-model="state.dialogVisible"
      width="80%"
      top="3%"
      height="800px"
      class="preview-dialog-box"
      destroy-on-close
      @click.stop
    >
      <template #header>
        <div>
          <span>预览 {{ state.filename }}&nbsp;&nbsp;</span>
          <el-button size="small" @click="downLoadFile()">下载</el-button>
        </div>
      </template>
      <office-preview
        v-if="state.showOfficeFile"
        :dialog-visible="state.showOfficeFile"
        ref="officeFileRef"
        :file-type="state.officeFileExtension"
        :file-url="state.officeFileUrl"
      />
      <!-- <div id="diffResult" class="context"></div> -->
      <iframe
        v-else-if="state.showIframe && !state.showOfficeFile"
        :src="state.fileSrc"
        frameborder="0"
        style="width: 100%; height: 99%"
      ></iframe>
      <div v-else v-html="state.prettyHtml" style="overflow: auto"></div>
    </firefly-dialog>
    <template v-if="props.showType == 'card'">
      <el-row>
        <el-col
          :xs="24"
          :sm="12"
          :md="12"
          :lg="12"
          :xl="12"
          v-for="(item, index) in dataList"
          :key="index"
          class="card-item"
        >
          <div class="flex card-item-box align-items-center">
            <div class="flex card-item-img" @click="downOrOpenFile(item)">
              <el-image
                class="card-img-icon"
                v-if="isImage(item?.filename) && props.showIcon"
                fit="cover"
                :src="loaded ? item[fileKey] : undefined"
                loading="eager"
              />
              <vab-icon
                v-else-if="props.showIcon"
                :icon="
                  'icon-' + getFileType(item.content_type ?? item.mimetype)
                "
                is-custom-svg
                class="card-img-icon"
              />
              <span class="white-space-nowrap">{{ item?.filename }}</span>
            </div>
            <div class="flex">
              <el-tooltip content="复制" placement="bottom" v-if="showCopyIcon">
                <div class="del-icon-box">
                  <vab-icon
                    class="copy-icon"
                    icon="copy"
                    is-custom-svg
                    @click.stop="copyContent(item[fileKey])"
                  />
                </div>
              </el-tooltip>
              <el-tooltip
                v-if="readonly != true"
                content="删除附件"
                placement="bottom"
              >
                <div class="del-icon-box">
                  <vab-icon
                    class="del-icon"
                    icon="delete"
                    is-custom-svg
                    @click.stop="deleteFile(item)"
                  />
                </div>
              </el-tooltip>
            </div>
          </div>
        </el-col>
      </el-row>
    </template>
    <template v-else>
      <div v-for="(item, index) in dataList" :key="index" class="file-item">
        <el-image
          v-if="isImage(item?.filename) && props.showIcon"
          fit="cover"
          :initial-index="imgIndex"
          :preview-src-list="srcList"
          :src="loaded ? item[fileKey] : undefined"
          loading="eager"
          @click="changeImgIndex(item[fileKey])"
        >
          <template #error>
            <div class="image-slot">
              <el-icon><icon-picture /></el-icon>
            </div>
          </template>
        </el-image>

        <vab-icon
          v-else-if="props.showIcon"
          :icon="
            'icon-' +
            getFileType(
              props.isRedmineFileServer
                ? item?.content_type ?? ''
                : item?.mimetype ?? ''
            )
          "
          is-custom-svg
          :style="{
            width: '18px',
            minWidth: '18px',
            maxHeight: '20px',
          }"
        />

        <div
          class="text filename-title"
          :class="
            props.isProductionReadOnly ? 'filename-title-cursor-none' : ''
          "
          @click="downOrOpenFile(item)"
        >
          <template v-if="item?.filename">{{ item?.filename }}</template>
          <template v-else>&nbsp;</template>
        </div>
        <div class="text filename-size" v-if="props.showIcon">
          <template v-if="item.filesize_text">
            （{{ item.filesize_text }}）
          </template>
          <template v-else-if="item.filesize">
            （{{ formatCapacity(item.filesize, false) }}）
          </template>
          <template v-else>&nbsp;</template>
          <template v-if="props.fileNameAppend != ''">
            {{ props.fileNameAppend }}
          </template>
        </div>
        <el-tooltip
          content="复制"
          placement="bottom"
          class="operation-icon"
          v-if="showCopyIcon"
        >
          <div class="del-icon-box">
            <vab-icon
              class="copy-icon"
              icon="copy"
              is-custom-svg
              @click.stop="copyContent(item[fileKey])"
            />
          </div>
        </el-tooltip>
        <el-tooltip
          v-if="readonly != true"
          content="删除附件"
          placement="bottom"
          class="operation-icon"
        >
          <div class="del-icon-box">
            <vab-icon
              class="del-icon"
              icon="delete"
              is-custom-svg
              @click.stop="deleteFile(item)"
            />
          </div>
        </el-tooltip>
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
  import FileSaver from 'file-saver'
  import { formatCapacity, copyContent } from '@/utils/common'
  import { getFileType } from '@/utils/index'
  import {
    loadDiff,
    fileExtensionsToPreview,
    loadLog,
    fileExtensionsForOffice,
  } from './fileUtil'
  import { Picture as IconPicture } from '@element-plus/icons-vue'
  import OfficePreview from '~/src/components/OfficePreview.vue'

  import 'diff2html/bundles/css/diff2html.min.css'

  const officeFileRef = ref<InstanceType<typeof OfficePreview> | null>(null)

  let fileKey = 'url'

  type FileItem = {
    filename: string
    url: string
    filesize_text: string
    content_type: string
    filesize: number
    mimetype?: string
    [key: string]: any // 允许其他动态键
  }
  const props = defineProps({
    showType: {
      type: String,
      default: 'list',
    },
    fileList: {
      type: [Array<FileItem>, Object],
      default: () => {
        return []
      },
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    showIcon: {
      type: Boolean,
      default: true,
    },
    customOpenImage: {
      type: Boolean,
      default: false,
    },
    showCopyIcon: {
      type: Boolean,
      default: true,
    },
    isRedmineFileServer: {
      type: Boolean,
      default: true,
    },
    isProductionDetail: {
      type: Boolean,
      default: false,
    },
    isProductionReadOnly: {
      type: Boolean,
      default: false,
    },
    isOneElement: {
      type: Boolean,
      default: false,
    },
    fileNameAppend: {
      type: String,
      default: '',
    },
  })
  const { fileList } = toRefs(props)
  const dataList = ref<Array<FileItem>>([])

  // 创建 FileItem 类型的示例
  const defaultFileItem: FileItem = {
    // 设置默认属性值
    filename: '',
    url: '',
    filesize_text: '',
    content_type: '',
    filesize: 0,
  }

  const loaded = ref(false)

  const state = reactive({
    officeFileExtension: '',
    officeFileUrl: '',
    prettyHtml: '',
    showOfficeFile: false,
    showIframe: true,
    showImage: false,
    dialogVisible: false,
    dialogVisible2: true,
    dialogIsOpen: false,
    fileSrc: '',
    filename: '',
    file: ref<FileItem>(defaultFileItem),
  })

  const imgIndex = ref(0)
  //预览图片，根据filelist返回图片数组
  const srcList = computed((): string[] => {
    if (isProductionReadOnly()) {
      return []
    }
    const str: string[] = []
    dataList.value.forEach((item) => {
      if (isImage(item?.filename)) {
        str.push(item[fileKey])
      }
    })
    return str
  })
  //点击图片，改变初始预览图像索引
  const changeImgIndex = (url: string) => {
    if (isProductionReadOnly()) {
      return
    }
    const i = srcList.value.findIndex((item) => {
      return item === url
    })
    imgIndex.value = i
  }

  watch(
    fileList,
    (v) => {
      if (!props.isRedmineFileServer && props.isProductionReadOnly) {
        return
      }

      // 生产订单文件列表 文件更新后不刷新问题
      if (!props.isRedmineFileServer) {
        dataList.value.length = 0
        if (props.isOneElement) {
          dataList.value.push(fileList.value)
        } else if (fileList.value.length > 1) {
          dataList.value.push(...Object.values(fileList.value))
        } else {
          dataList.value.push(fileList.value[0])
        }
        return
      }

      dataList.value.length = 0
      if (fileList.value) {
        dataList.value.push(...Object.values(fileList.value))
      }
    },
    { deep: true }
  )
  const isImage = (fileName: string) => {
    if (!fileName) {
      return
    }
    const formatNum = fileName.lastIndexOf('.')
    let fType = fileName.slice(formatNum + 1, fileName.length)
    fType = fType.toLowerCase()
    if (
      fType === 'png' ||
      fType === 'jpg' ||
      fType === 'jpeg' ||
      fType === 'gif' ||
      fType === 'tiff' ||
      fType === 'svg' ||
      fType === 'ico' ||
      fType === 'bmp'
    ) {
      return true
    }
    return false
  }

  const emit = defineEmits([
    'on-remove',
    'image-show-status-change',
    'viewer-image',
  ])
  const $baseConfirm: any = inject('$baseConfirm')

  const isProductionReadOnly = () => {
    return props.isProductionReadOnly
  }

  /**
   * 删除附件
   */
  const deleteFile = (file: any) => {
    if (isProductionReadOnly()) {
      return
    }

    $baseConfirm(
      '确认是否删除该' + (props.showIcon ? '附件' : '文件'),
      null,
      () => {
        emit('on-remove', file)
      }
    )
  }

  const downLoadFile = (file: FileItem = state.file) => {
    if (isProductionReadOnly()) {
      return
    }
    $baseConfirm('是否进行下载？', null, async () => {
      // 文件直接下载
      fetch(file[fileKey])
        .then((res) => res.blob())
        .then((blob) => {
          let fileBlob = new Blob([blob], {
            type: file.content_type,
          })
          FileSaver.saveAs(fileBlob, file.filename)
        })
        .catch(() => {
          FileSaver.saveAs(file[fileKey], file.filename)
        })
    })
  }

  const downOrOpenFile = (file: FileItem) => {
    if (isProductionReadOnly()) {
      return
    }
    state.showIframe = true
    state.file = file
    if (file.filename) {
      state.fileSrc = file[fileKey]
      state.filename = file.filename
      const fileExtension = state.filename.split('.').pop()?.toLowerCase() || ''

      if (fileExtension == 'patch') {
        state.showIframe = false
        state.dialogVisible = true
        loadDiff(file)
          .then((result) => {
            state.prettyHtml = result
          })
          .catch((error) => {
            console.error('Error loading diff:', error)
            state.prettyHtml = ''
          })

        return
      }
      if (fileExtension == 'log') {
        state.showIframe = false
        state.dialogVisible = true
        loadLog(state.file)
          .then((result) => {
            state.prettyHtml = result
          })
          .catch((error) => {
            console.error('Error loading diff:', error)
            state.prettyHtml = ''
          })
        return
      }

      // 20240311 16:30开会 说MP4要直接点开直接播放
      if (
        state.file?.content_type?.indexOf('video') >= 0 ||
        ['mp4', 'avi', 'mov', 'flv', 'wmv', 'mkv', 'rmvb'].includes(
          fileExtension
        )
      ) {
        state.showIframe = true
        state.dialogVisible = true
        return
      }

      if (!isImage(file.filename)) {
        state.showIframe = false
        // 判断常见的文件格式,能在线直接预览的格式，即浏览器能直接看的格式
        if (!fileExtensionsToPreview.includes(fileExtension)) {
          $baseConfirm('该文件不能在线预览，是否进行下载？', null, async () => {
            // 文件直接下载
            fetch(file[fileKey])
              .then((res) => res.blob())
              .then((blob) => {
                let fileBlob = new Blob([blob], {
                  type: file.content_type,
                })
                FileSaver.saveAs(fileBlob, file.filename)
              })
              .catch(() => {
                FileSaver.saveAs(file[fileKey], file.filename)
              })
          })
        } else {
          if (fileExtensionsForOffice.includes(fileExtension)) {
            state.dialogVisible = true
            state.showOfficeFile = true

            setTimeout(() => {
              // 等待组件刷新生成完毕
              if (officeFileRef.value) {
                console.log('user setstate')
                state.officeFileExtension = fileExtension
                state.officeFileUrl = file[fileKey]
                // officeFileRef.value.setState(fileExtension, file[fileKey])
                // officeFileRef.value.showEdit()
              }
            }, 200)
          } else {
            window.open(file[fileKey])
          }
          // state.showIframe = true
          // state.dialogVisible = true
        }
      } else {
        if (props.customOpenImage) {
          let param: any = {
            // index: dataList.value.findIndex((item) => item[fileKey] === file[fileKey]),
            list: dataList.value
              .filter((item) => {
                return isImage(item?.filename)
              })
              .map((item) => item[fileKey]),
          }
          param['index'] = param.list.findIndex((item: any) => {
            return item === file[fileKey]
          })
          emit('viewer-image', param)
        } else {
          changeImgIndex(file[fileKey])
          state.showImage = true
        }

        emit('image-show-status-change', true)
      }
    }
  }

  const closeViewer = () => {
    state.showImage = false
    emit('image-show-status-change', false)
  }

  // const isImage = (filename: string): boolean => {
  //   return /\.(jpg|jpeg|png|gif|bmp|webp|svg)$/.test(filename)
  // }
  watch(
    () => state.dialogVisible,
    () => {
      if (state.dialogVisible == false) {
        state.showOfficeFile = false
      }
    }
  )

  // 用于监听dom树变化
  let observer: MutationObserver | null = null

  const hidePopperElements = () => {
    const targetItems = document.getElementsByClassName(
      'el-popper is-dark'
    ) as HTMLCollectionOf<HTMLElement>
    // console.log('targetItems:', targetItems)
    for (let item of targetItems) {
      item.style.display = 'none'
    }
  }

  const startObserving = () => {
    const config = { childList: true, subtree: true }
    const callback = (mutationsList: MutationRecord[]) => {
      for (const mutation of mutationsList) {
        if (mutation.type === 'childList') {
          hidePopperElements()
        }
      }
    }
    observer = new MutationObserver(callback)
    observer.observe(document.body, config)
  }

  const stopObserving = () => {
    if (observer) {
      observer.disconnect()
      observer = null
    }
  }

  onMounted(async () => {
    dataList.value.length = 0

    if (fileList.value) {
      const files = Array.isArray(fileList.value)
        ? Object.values(fileList.value)
        : [fileList.value]
      dataList.value.push(...(props.isOneElement ? [files[0]] : files))
    }

    if (!props.isRedmineFileServer) {
      fileKey = 'full_url'
    }
    setTimeout(() => {
      loaded.value = true
    }, 2000)

    // el-dialog在el-table中弹出使用时，如果该列有show-overflow-tip属性，el-dialog的#header插槽会致使该处错误弹出tooltip
    watch(
      () => state.dialogVisible,
      (newValue) => {
        if (newValue) {
          startObserving()
          hidePopperElements()
        } else {
          stopObserving()
        }
      },
      { immediate: true }
    )
  })

  onUnmounted(() => {
    stopObserving()
  })
</script>

<style lang="scss">
  .image-slot {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    background-color: #f0f0f0;
  }
  .file-list .file-item .el-image {
    width: 18px;
    .el-image__inner {
      max-width: 18px;
      transform: translateZ(0); // 使用GPU
      will-change: transform;
    }
  }
</style>

<style lang="scss" scoped>
  use {
    width: 100%;
    height: 100%;
  }
  :deep(.vab-icon) {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .del-icon,
  .copy-icon {
    cursor: pointer !important;
  }
  .file-list {
    .file-item {
      position: relative;
      display: flex;
      align-items: center;
      max-width: 100%;
      // padding: 5px;
      margin-right: 16px;
      margin-bottom: 8px;
      overflow-x: auto;
      overflow-y: hidden;
      // background: #f6f6f6;
      border-radius: 4px;

      .del-icon-box {
        color: #999;
        cursor: pointer;
        &:hover {
          color: $base-color-primary;
        }
      }
      .del-icon,
      .copy-icon {
        width: 22px;
        height: 22px;
        &:hover {
          transform: scale(1.1);
        }
      }
      .img {
        display: block;
        margin-right: 20px;
      }
      .text {
        padding-left: 6px;
        overflow: hidden;
        color: #333;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .filename-title {
        font-size: 14px;
        cursor: pointer !important;
        &:hover {
          color: $base-color-primary;
        }
      }
      .filename-title-cursor-none {
        pointer-events: none !important;
      }
      .filename-size {
        padding-top: 2px;
        font-size: 12px;
        color: #9199a3;
      }
    }
  }
  .filename div {
    line-height: 16px !important;
  }

  :deep() {
    .firefly-dialog .el-dialog__body {
      overflow: auto;
    }

    // https://github.com/rtfpessoa/diff2html/issues/381#top   Line Number Row is overflowing when it's wrapped inside a fixed height div.
    .d2h-wrapper {
      transform: translateZ(0);
    }
    .d2h-diff-table {
      position: relative;
    }
    .d2h-code-side-linenumber {
      position: fixed;
    }
  }
  // }
  .card-img-icon {
    max-width: 16.8px;
    max-height: 16.8px;
    margin-right: 4px;
  }
  .card-item {
    padding: 0px 8px;
    margin-bottom: 8px;
  }

  .card-item-box {
    justify-content: space-between;
    padding: 8px 10px;
    background-color: $bg-color-active;
    border-radius: 6px;
  }
  .card-item-img {
    max-width: calc(100% - 40px);
    cursor: pointer;
  }
  .operation-icon {
    cursor: pointer !important;
  }
</style>
