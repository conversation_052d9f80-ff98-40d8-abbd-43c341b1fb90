<script lang="ts" setup>
  import { getList, getMemberList } from '@/api/projectIssue'
  import { getList as getProjectList } from '@/api/projectIndex'
  import { useSettingsStore } from '@/store/modules/settings'
  import { useUserStore } from '@/store/modules/user'
  import { globalSearch } from '@/api/index'
  import { IssueIconChild, IssueIconParent } from '@/utils/index'
  import CommonIcon from '~/src/components/CommonIcon.vue'
  import CommonEmpty from '~/src/components/CommonEmpty.vue'
  import IssueStatus from '@/components/IssueStatus.vue'
  import { IconType } from '~/src/icon'
  import { cloneDeep } from 'lodash'
  import EditDialog from '~/src/views/project/issue/components/IssueEditDialog.vue'
  import PersonnelSelect from '~/src/components/PersonnelSelect.vue'
  import ProjectSelectUsePinyin from '~/src/components/ProjectSelectUsePinyin.vue'
  import { getRedmineUserList } from '@/api/user'

  import { ref, Ref } from 'vue'
  const settingsStore = useSettingsStore()
  const router = useRouter()
  const { theme, collapse } = storeToRefs(settingsStore)
  const userStore = useUserStore()
  let { redmine, user_settings, user_id } = userStore

  const editRef: Ref<typeof EditDialog | null> = ref(null)

  const searchText = ref('')
  const inputRef = ref()
  const visible = ref(false)

  const activeName = ref('issue')

  const dataLoading = ref(false)

  const dialogHeight = computed(() => {
    const h = document.body.clientHeight * 0.75
    return h
  })

  const props = defineProps({
    isPopups: {
      type: Boolean,
      default: false,
    },
  })
  const searchBtn = ref([
    {
      label: '全部',
      value: 'all',
    },
    {
      label: '事项',
      value: 'issue',
    },
    {
      label: '项目',
      value: 'project',
    },
    {
      label: '产品',
      value: 'product',
    },
  ])

  const searchResult = ref({
    //这里为了能直接赋值，用ref
    issue: {
      name: '',
      data: [] as any,
    },
    product: {
      name: '',
      data: [] as any,
    },
    project: {
      name: '',
      data: [] as any,
    },
    tchipbbs: {
      name: '',
      data: [] as any,
    },
  })
  const state = reactive({
    value: 9,
    width: 400,
    titleMaxWidth: '100%',
    listHeight: !props.isPopups ? 'calc(100vh - 200px)' : '100%',
    searchTimer: null as NodeJS.Timeout | null,
    list: [] as any,
    projectList: [] as any,
    projectListCopy: [],
    projectMemberList: [] as any,
    projectMemberListCopy: [],
    queryForm: {
      filter: {
        search: 'issue',
        'projects.id': null,
        assigned_to_id: null,
        author_id: null,
      },
      op: {
        assigned_to_id: null,
        author_id: null,
      } as any,
      key: '',
      pageNo: 1,
      limit: 20,
    },
    total: 0,
    layout: 'total, sizes, prev, pager, next, jumper',
  })

  const queryProject = async () => {
    const { data } = await getProjectList({
      filter: {
        projectsExt: { id: 0, project_type: 'product_type' },
      },
      op: {
        projectsExt: { id: '>', project_type: '<>' },
      },
      is_tree: 'no',
    })
    state.projectList = data
    state.projectListCopy = data
  }

  const queryMemberList = async () => {
    let queryForm: any = {
      limit: 100,
      filter: { user: { type: 'User' } },
    }
    if (state.queryForm.filter['projects.id']) {
      queryForm.filter['project_id'] = state.queryForm.filter['projects.id']
    } else {
      const res = await getRedmineUserList({
        filter: { status: 1, auth: true },
        limit: 2000,
      })
      state.projectMemberList = [...res.data.data]
      return
    }

    // 选择了项目的，列表筛选人员替换成该项目成员
    const {
      data: { data },
    } = await getMemberList(queryForm)

    // 使用 Map 进行去重
    const uniqueMembersMap = new Map()

    let memberList = data.map((item: any) => ({
      ...item.user,
    }))
    memberList.forEach((item: any) => {
      if (!uniqueMembersMap.has(item.id)) {
        uniqueMembersMap.set(item.id, item)
      }
    })

    // 将去重后的成员列表赋值给 state.projectMemberList
    state.projectMemberList = Array.from(uniqueMembersMap.values())
  }

  const filterMethod = (query: string) => {
    calcTitleMaxWidth()
    if (query) {
      state.projectList = cloneDeep(
        state.projectListCopy.filter((item: any) => {
          if (
            item.name.toLowerCase().indexOf(query.toLowerCase()) !== -1 ||
            item.id.toString().indexOf(query) >= 0
          ) {
            return true
          }
        })
      )
    } else {
      state.projectList = cloneDeep(state.projectListCopy)
    }
  }

  const handleSizeChange = (size: number) => {
    state.queryForm.limit = size
    state.queryForm.pageNo = 1
    fetchData()
  }

  const handleCurrentChange = (page: number) => {
    state.queryForm.pageNo = page
    fetchData()
  }

  const changeSearch = (searchType: string) => {
    activeName.value = searchType
    state.queryForm.filter['search'] = searchType
    state.queryForm.pageNo = 1
    fetchData()
  }

  const handleSearch = async (from = '') => {
    console.log('handleSearch')
    dataLoading.value = true
    calcTitleMaxWidth()

    if (from == 'projectIdChange') {
      queryMemberList()
    }

    const t = searchText.value.replace(/\s/, '')

    if (t == '') {
      dataLoading.value = false
      return
    }

    if (state.searchTimer) {
      clearTimeout(state.searchTimer)
      state.searchTimer = null
    }
    if (
      t == '' &&
      !state.queryForm.filter['projects.id'] &&
      !state.queryForm.filter.assigned_to_id
    ) {
      return
    }
    if (
      state.queryForm.filter['projects.id'] &&
      state.queryForm.filter['projects.id'] > 0
    ) {
      queryMemberList()
    }
    state.queryForm.pageNo = 1
    state.searchTimer = setTimeout(async () => {
      await fetchData()
      dataLoading.value = false
    }, 800)
  }

  const fetchData = async () => {
    visible.value = true
    dataLoading.value = true
    if (!searchText.value) {
      state.list = []
      setTimeout(() => {
        dataLoading.value = false
      }, 500)
      return
    }
    state.queryForm.key = searchText.value
    state.list = []
    const { data } = await globalSearch(state.queryForm)
    dataLoading.value = false
    if (data) {
      if (props.isPopups) {
        // 如果是在弹窗中，需要重新计算高度
        const el = document.querySelector('.vab-dialog-body')
        let height = el?.clientHeight
        if (height) {
          state.listHeight = height - 100 + 'px'
        }
      }
      if (state.queryForm.key) {
        const keywords = state.queryForm.key
          .split(' ')
          .map((key) => key.trim())
          .filter((key) => key.length > 0)
        data.data.forEach((item: any) => {
          let hasKeyword = false
          let firstKeywordIndex = -1
          // 查找第一个包含任何关键词的位置
          keywords.forEach((keyword) => {
            const position = item.description?.indexOf(keyword)
            if (
              position > 200 &&
              (firstKeywordIndex === -1 || position < firstKeywordIndex)
            ) {
              firstKeywordIndex = position
              hasKeyword = true
            }
          })
          if (hasKeyword && firstKeywordIndex !== -1) {
            const lines = item.description.split('\r\n')
            const firstIndex = lines.findIndex((line: string) =>
              keywords.some((keyword) => line.includes(keyword))
            )

            // 从第一个包含关键词的行开始截取数组
            const resultLines = firstIndex !== -1 ? lines.slice(firstIndex) : []
            const resultString = resultLines.join('\r\n')
            item.key_string = resultString
          } else {
            item.key_string = item.description
          }
          // 用关键词高亮显示
          let highlightedName = item.name
          let highlightedDescription = item.key_string

          keywords.forEach((keyword) => {
            const regex = new RegExp(`(${keyword})`, 'gi') // 创建正则表达式以实现高亮
            highlightedName = highlightedName.replace(
              regex,
              `<span class="color-danger">$1</span>`
            )
            highlightedDescription = highlightedDescription.replace(
              regex,
              `<span class="color-danger">$1</span>`
            )
          })

          item.name_html = highlightedName
          item.description_html = highlightedDescription
          state.list.push(item)
        })
      } else {
        state.list = data.data
      }
      state.total = data.total
    }
  }

  // 输入数字直接跳转到对应id的事项
  const handleEnter = async () => {
    calcTitleMaxWidth()
    const t = searchText.value.replace(/\s/, '')
    let text2 = null
    if (/^#\d+$/.test(t)) {
      text2 = t.substring(1)
    } else if (/^\d{1,7}$/.test(t)) {
      // 匹配小于7位的数字 即事项编号
      text2 = t
    } else {
      handleSearch()
      return
    }
    const response = await getList({ filter: { id: text2 ?? t } })
    let data = response.data.data
    if (data.length == 0 || !data) {
      state.queryForm.key = text2 ?? t
      state.queryForm.pageNo = 1
      fetchData()
      return
    }
    data = data[0]
    let routeUrl = router.resolve({
      path: '/project/detail',
      query: { issue_id: data.id, project_id: data.project_id },
    })
    window.open(routeUrl.href, '_blank')

    handleSearch() // 跳转后也需要搜索
  }

  //跳转，需要根据不同类型进行跳转
  const toPage = (info: any, index: string, openFrom = '') => {
    const obj: any = {
      issue: () => {
        if (!props.isPopups) {
          if (user_settings.issue_open_type == 'dialog') {
            editRef.value?.showEdit({ id: info.id }, 'dialog')
          } else if (user_settings.issue_open_type == 'blank') {
            let routeUrl = router.resolve({
              path: '/project/detail',
              query: { issue_id: info.id, project_id: info.project_id },
            })
            window.open(routeUrl.href, '_blank')
          } else if (user_settings.issue_open_type == 'drawer') {
            editRef.value?.showEdit({ id: info.id }, 'drawer')
          }
        } else {
          let routeUrl = router.resolve({
            path: '/project/detail',
            query: { issue_id: info.id, project_id: info.project_id },
          })
          window.open(routeUrl.href, '_blank')
        }
      },
      product: () => {
        let routeUrl = router.resolve({
          path: '/project/productDetailsIndex',
          query: { product_id: info.id },
        })
        window.open(routeUrl.href, '_blank')
      },
      project: () => {
        let routeUrl = router.resolve({
          path: '/project/issue',
          query: {
            project_id: openFrom == 'issue' ? info.project_id : info.id,
          },
        })
        window.open(routeUrl.href, '_blank')
      },
      tchipbbs: () => {
        let routeUrl = router.resolve({
          path: '/culture/thread',
          query: { id: info.id },
        })
        window.open(routeUrl.href, '_blank')
      },
    }

    const fn = obj[index]
    fn && fn()
  }

  const closePop = () => {
    visible.value = false
  }

  //搜索是否完全无结果
  const isEmpty = computed(() => {
    const l1 = searchResult.value.issue.data.length
    const l2 = searchResult.value.product.data.length
    const l3 = searchResult.value.project.data.length
    const l4 = searchResult.value.tchipbbs.data.length

    if (l1 || l2 || l3 || l4) {
      return false
    }
    return true
  })

  const openDialog = () => {
    visible.value = true
  }

  const getIssueTypeIcon = (row: any) => {
    if (row.parent_id) {
      //只要是子issue，都返回父的icon
      return IssueIconChild[row.tracker_id]
    }
    return IssueIconParent[row.tracker_id]
  }

  const calcTitleMaxWidth = () => {
    let tmp = !props.isPopups
      ? document.querySelector('.change-el-tabs-content')?.clientWidth
      : document.querySelector('.vab-dialog-body')?.clientWidth

    if (tmp) {
      tmp -= 500
      state.titleMaxWidth = `${tmp}px`
    }
  }

  const handleStorageEvent = (event: StorageEvent) => {
    if (event.key === 'user_settings_' + user_id + '_') {
      // 从 localStorage 获取最新的 user_settings
      const localStorageUserSettings = localStorage.getItem(
        'user_settings_' + user_id + '_'
      )
      user_settings = localStorageUserSettings
        ? JSON.parse(localStorageUserSettings)
        : user_settings
    }
  }

  onMounted(() => {
    queryProject()
    queryMemberList()
    calcTitleMaxWidth()

    const localStorageUserSettings = localStorage.getItem(
      'user_settings_' + user_id + '_'
    )
    user_settings = localStorageUserSettings
      ? JSON.parse(localStorageUserSettings)
      : user_settings

    // 监听localstorage变化的事件
    window.addEventListener('storage', handleStorageEvent)
  })
</script>

<template>
  <div
    :class="!props.isPopups ? 'change-el-tabs-content' : ''"
    style="height: 100%"
  >
    <div @click="visible = true" style="height: 100%">
      <vab-query-form>
        <vab-query-form-left-panel :span="23">
          <el-form-item>
            <el-input
              v-model="searchText"
              @keyup.enter="handleEnter"
              clearable
              style="width: 275px"
            >
              <template #prefix>
                <CommonIcon :type="IconType.Search" />
              </template>
              <template #append>
                <div style="user-select: none" @click="handleEnter">搜索</div>
              </template>
            </el-input>
          </el-form-item>
          <el-form-item>
            <!-- <el-select
              v-model="state.queryForm.filter['projects.id']"
              @change="handleSearch('projectIdChange')"
              style="width: 180px"
              filterable
              clearable
              placeholder="请选择项目"
              :filter-method="filterMethod"
            >
              <el-option
                v-for="pItem in state.projectList"
                :key="pItem.id"
                :value="pItem.id"
                :label="pItem.name || ''"
              >
                {{ pItem.name }}
              </el-option>
            </el-select> -->
            <ProjectSelectUsePinyin
              v-model="state.queryForm.filter['projects.id']"
              :option-list="state.projectList"
              @change="handleSearch('projectIdChange')"
              style="width: 180px"
            />
          </el-form-item>
          <el-form-item>
            <!-- <el-select
              v-model="state.queryForm.filter.assigned_to_id"
              clearable
              placeholder="请选择处理人"
              style="width: 180px"
              @change="handleSearch"
              filterable
            >
              <el-option
                v-for="item in state.projectMemberList"
                :key="item.user_id"
                :label="item.name"
                :value="item.user_id"
              />
            </el-select> -->
            <PersonnelSelect
              v-model="state.queryForm.filter.assigned_to_id"
              :option-list="state.projectMemberList"
              :placeholder-text="'请选择处理人'"
              @change="handleSearch"
              style="width: 180px"
            />
          </el-form-item>
          <el-form-item>
            <PersonnelSelect
              v-model="state.queryForm.filter.author_id"
              :option-list="state.projectMemberList"
              :placeholder-text="'请选择创建人'"
              @change="handleSearch"
              style="width: 180px"
            />
          </el-form-item>
        </vab-query-form-left-panel>
      </vab-query-form>
      <!-- <div class="flex" style="align-items: center; padding-bottom: 10px">
        <div
          v-for="(bItem, bKey) in searchBtn"
          style="height: 32px; cursor: pointer"
          :class="[activeName == bItem.value ? 'active-btn' : '', 'search-btn']"
          type="primary"
          color="#F2F2F2"
          text
          :key="bKey"
          @click="changeSearch(bItem.value)"
        >
          {{ bItem.label }}
        </div>
      </div> -->
      <div
        v-if="state.list.length == 0"
        v-loading="dataLoading"
        class="common-empty"
      >
        <CommonEmpty />
      </div>
      <div v-else>
        <div
          class="result-list"
          :style="{ height: state.listHeight, 'overflow-x': 'hidden' }"
        >
          <template v-for="(item, index) in state.list" :key="index">
            <div class="search-item" v-if="item.type == 'issue'">
              <el-row :gutter="24" style="margin-right: 0px">
                <el-col
                  :span="1"
                  class="search-item-first"
                  style="max-width: 40px"
                >
                  <CommonIcon
                    :issue-type="
                      IssueIconParent[item.tracker_id] ?? IssueIconParent[0]
                    "
                    :size="20"
                  />
                </el-col>
                <el-col :span="23" style="padding-left: 4px">
                  <div class="flex align-items-center search-item-first">
                    <el-tooltip
                      :content="item.name"
                      :hide-after="50"
                      placement="top-start"
                      style="max-width: 300px"
                    >
                      <span
                        @click="toPage(item, item.type)"
                        class="search-item-title"
                        :style="{ maxWidth: state.titleMaxWidth }"
                        v-html="item.name_html"
                      ></span>
                    </el-tooltip>
                    <span class="text-color-secondary inline-dot">•</span>
                    <span
                      class="search-item-subject"
                      @click="toPage(item, 'project', 'issue')"
                      style="cursor: pointer"
                    >
                      {{ item.project_name }}
                    </span>
                    <el-tag
                      type="info"
                      class="module-name margin-right-1"
                      style="background-color: white"
                      v-if="item.category_text"
                    >
                      {{ item.category_text.name }}
                    </el-tag>
                    状态：
                    <IssueStatus
                      v-model:value="item.status_id"
                      class="margin-right-1"
                      :only-show-tag="true"
                    />
                    <span class="search-item-subject">
                      {{ item.created_on }}
                    </span>
                  </div>
                </el-col>
              </el-row>
              <el-row :gutter="24" style="margin-right: 0px">
                <el-col :span="1" style="max-width: 40px">&nbsp;</el-col>
                <el-col :span="23" class="search-item-content">
                  <span v-html="item.description_html"></span>
                </el-col>
              </el-row>
            </div>
            <div
              class="search-item"
              v-else-if="item.type == 'project' || item.type == 'product'"
            >
              <el-row :gutter="24">
                <el-col
                  :span="1"
                  class="search-item-first"
                  style="max-width: 40px"
                >
                  <CommonIcon :type="IconType.Search" :size="20" />
                </el-col>
                <el-col :span="23">
                  <div class="flex align-items-center search-item-first">
                    <el-tooltip
                      :content="item.name"
                      :hide-after="50"
                      placement="top-start"
                      style="max-width: 300px"
                    >
                      <span
                        class="search-item-title"
                        :style="{ maxWidth: state.titleMaxWidth }"
                        v-html="item.name_html"
                        @click="toPage(item, item.type)"
                      ></span>
                    </el-tooltip>
                    <span class="text-color-secondary inline-dot">•</span>
                    <span
                      class="search-item-subject"
                      v-if="item.type == 'project'"
                    >
                      项目
                    </span>
                    <span
                      class="search-item-subject"
                      v-else-if="item.type == 'product'"
                    >
                      产品
                    </span>
                    <span class="search-item-subject">
                      {{ item.created_on }}
                    </span>
                  </div>
                </el-col>
              </el-row>
              <el-row :gutter="24">
                <el-col :span="1" style="max-width: 40px">&nbsp;</el-col>
                <el-col :span="23" class="search-item-content">
                  <span v-html="item.description_html"></span>
                </el-col>
              </el-row>
            </div>
          </template>
        </div>
        <el-pagination
          background
          v-model:currentPage="state.queryForm.pageNo"
          :layout="state.layout"
          v-model:pageSize="state.queryForm.limit"
          :total="state.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <edit-dialog ref="editRef" :height="dialogHeight" />
  </div>
</template>

<style lang="scss" scoped>
  :deep() {
    .el-input-group__append {
      color: $base-color-white;
      cursor: pointer;
      background-color: $base-color-primary;
      box-shadow: none;
    }
  }
  .change-el-tabs-content {
    :deep(.el-tabs__content) {
      height: calc(100vh - 195px);
    }
  }

  .search-box {
    // display: flex;
    // align-items: center;
    // width: 445px;
    height: 40px;
    margin: 0px 12px;
  }

  .search-input {
    width: 100%;
    cursor: pointer;
    :deep {
      .el-input__wrapper {
        height: 40px;
        background-color: #fff;
        border: 1px solid #dcdfe6;
        border-radius: 6px;
        box-shadow: none;
      }
      .el-input__prefix-inner {
        color: #333;
      }

      .el-input__inner {
        color: #fff;
      }

      .el-input__inner::placeholder {
        font-size: 13px;
        color: #666;
      }
    }
  }

  // :deep(.el-tabs__nav) {
  //   display: flex;
  //   justify-content: space-between;
  //   width: 100%;
  //   height: 35px;
  //   padding: 0px 10px;
  // }

  :deep(.el-tabs__item) {
    color: #999;

    &.is-active {
      color: $base-color-primary;
    }
  }

  // :deep(.el-avatar) {
  //   background: transparent;
  // }

  :deep(.el-empty) {
    padding-top: 0px;
    padding-bottom: 0px;
  }

  :deep(.el-tabs__nav-wrap::after) {
    background: transparent;
  }

  :deep(.el-tabs__content) {
    height: 500px;
    overflow-y: auto;
    // border-top: 1px solid #f2f4f6;
  }
  :deep(.el-tabs__header) {
    margin-bottom: 9px;
    border-bottom: 1px solid #f2f4f6;
  }

  :deep(.el-popover) {
    padding: 0px;
  }

  .type-title {
    padding: 10px;
    font-size: 14px;
    color: #999999;
  }

  .result-list {
    .result-item {
      display: flex;
      align-items: center;
      height: 40px;
      padding: 0px 10px;
      margin-bottom: 8px;
      font-size: 14px;
      font-weight: 600;
      list-style: none;
      cursor: pointer;
      border-radius: 6px;
      &:hover {
        background: #f2f4f6;
      }

      .result-item-flex {
        display: flex;
        align-items: center;
      }
    }
  }

  .search-icon-inner {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 56px;
    height: 40px;
    margin: 0 auto;
    cursor: pointer;
    background-color: #fff;
    border: 1px solid #dcdfe6;
    border-radius: 5px;

    &:hover {
      background-color: $base-color-hover;
    }
  }
  .handle-search-btn {
    cursor: pointer;
    background-color: $base-color-primary;
  }
  .search-btn {
    padding: 10px 16px;
    margin-right: 16px;
    font-size: 14px;
    line-height: 14px;
    color: #606266;
    background-color: #f2f2f2;
    border-radius: 5px;
  }
  .active-btn {
    color: $base-color-primary !important;
    background-color: mix(
      $base-color-white,
      $base-color-primary,
      80%
    ) !important;
  }
  .search-item {
    padding: 16px 0px;
    .search-item-first {
      height: 22px;
      margin-right: -5px;
      margin-bottom: 8px;
    }
    .search-item-title {
      margin-right: 8px;
      // max-width: calc(100vw - 1111px);
      overflow: hidden;
      font-size: 16px;
      line-height: 20px;
      color: $base-color-text-title;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .search-item-title:hover {
      color: $base-color-primary;
      cursor: pointer;
    }
    .search-item-subject {
      margin: 0px 7px;
      color: $base-color-text-desc;
    }
    .margin-right-1 {
      margin-right: 20px;
    }
    .search-item-content {
      padding-left: 0px !important;
      color: $base-color-text-desc;
      span {
        display: -webkit-box; /* 将元素设置为块状容器 */
        max-height: calc(16px * 2); /* 根据字体大小计算每行所需的高度 */
        overflow: hidden; /* 隐藏超过指定高度的内容 */
        text-overflow: ellipsis; /* 当内容被省略时显示省略号(...) */
        line-clamp: 2; /* 控制显示的行数 */
      }
    }
  }
  .common-empty {
    display: flex;
    align-items: center;
    justify-content: center;
    height: calc(100% - 86px);
  }
  .result-list {
    height: calc(100% - 86px);
    overflow-y: auto;
  }
</style>

<style lang="scss">
  .search-dialog {
    .el-dialog__body {
      padding-top: 15px;
    }
  }
</style>
