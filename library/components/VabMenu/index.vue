<template>
  <component :is="menuComponent" v-if="!item.meta.hidden" :item-or-menu="item">
    <template v-if="item.children && item.children.length">
      <el-scrollbar
        v-if="
          (layout === 'horizontal' && item.children.length > 18) ||
          (layout !== 'horizontal' && collapse && item.children.length > 18)
        "
        class="vab-menu-children-height"
      >
        <vab-menu
          v-for="route in item.children"
          :key="route.path"
          :item="route"
        />
      </el-scrollbar>
      <template v-else>
        <vab-menu
          v-for="route in item.children"
          :key="route.path"
          :item="route"
        />
      </template>
    </template>
  </component>
</template>

<script lang="ts">
  /* 防止偶发性自动导入失败 */
  import { computed, defineComponent } from 'vue'
  import { storeToRefs } from 'pinia'
  import { useSettingsStore } from '@/store/modules/settings'

  const imports = require.context('./components', true, /\.vue$/)
  const Components: {
    [key: string]: any
  } = {}
  imports.keys().forEach((key) => {
    Components[key.replace(/(\/|\.|vue)/g, '')] = imports(key).default
  })

  export default defineComponent({
    name: 'VabMenu',
    components: Components,
    props: {
      item: {
        type: Object,
        required: true,
      },
      layout: {
        type: String,
        default: '',
      },
    },
    setup(props) {
      const settingsStore = useSettingsStore()
      const { collapse } = storeToRefs(settingsStore)

      const menuComponent = computed(() =>
        props.item.children &&
        props.item.children.some((_route: any) => {
          return _route.meta.hidden !== true
        })
          ? 'VabSubMenu'
          : 'VabMenuItem'
      )

      return {
        collapse,
        menuComponent,
      }
    },
  })
</script>

<style lang="scss" scoped>
  .vab-menu-children-height {
    height: 60vh !important;
    overflow-y: auto;

    &::-webkit-scrollbar {
      width: 0;
      height: 0;
    }
  }
</style>

<!--由于element-plus
bug使用popper-append-to-body=false会导致多级路由无法显示，故所有菜单必须生成至body下，样式必须放到body下-->
<style lang="scss">
  .el-popper.is-light {
    border: 0 !important;
  }

  .custom-menu {
    margin: 0px 12px;
    overflow-y: auto;

    & > .el-sub-menu > .el-sub-menu__title {
      // padding: 0px 8px !important;
    }
    .el-sub-menu.is-active {
      & > .el-sub-menu__title {
        font-weight: 900;
        color: $base-color-primary !important;
      }
    }

    .el-menu-item.is-active {
      font-weight: 900;
      color: $base-color-primary !important;
    }

    .el-sub-menu__title:hover {
      background-color: $base-color-hover;
    }

    .el-sub-menu,
    .el-menu-item {
      margin-top: 5px;
    }

    .custom-size {
      width: 24px;
      height: 24px;
    }
  }
</style>
