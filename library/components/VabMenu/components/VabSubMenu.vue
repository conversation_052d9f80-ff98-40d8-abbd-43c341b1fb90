<script lang="ts" setup>
  defineProps({
    itemOrMenu: {
      type: Object,
      default() {
        return null
      },
    },
  })
</script>

<template>
  <!--is-custom-svg 默认为svg，需导入到项目中-->
  <el-sub-menu :index="itemOrMenu.path" teleported>
    <template #title>
      <vab-icon
        v-if="itemOrMenu.meta.icon"
        :icon="itemOrMenu.meta.icon"
        :is-custom-svg="true"
        :title="itemOrMenu.meta.title"
        class="custom-size"
      />
      <span :title="itemOrMenu.meta.title">
        {{ itemOrMenu.meta.title }}
      </span>
    </template>
    <slot />
  </el-sub-menu>
</template>
