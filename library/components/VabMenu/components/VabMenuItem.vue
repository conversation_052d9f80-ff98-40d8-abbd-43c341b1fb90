<script lang="ts" setup>
  import CommonIcon from '~/src/components/CommonIcon.vue'
  import { useSettingsStore } from '@/store/modules/settings'
  import { isExternal } from '@/utils/validate'
  import { isHashRouterMode } from '@/config'
  import { VabRoute } from '/#/router'

  const props = defineProps({
    itemOrMenu: {
      type: Object,
      default() {
        return null
      },
    },
  })

  const route: VabRoute = useRoute()
  const router = useRouter()

  const $pub: any = inject('$pub')

  const settingsStore = useSettingsStore()
  const { device } = storeToRefs(settingsStore)
  const { foldSideBar } = settingsStore

  const handleLink = () => {
    const routePath = props.itemOrMenu.path
    const target = props.itemOrMenu.meta.target
    if (target === '_blank') {
      if (isExternal(routePath)) window.open(routePath)
      else if (route.path !== routePath)
        isHashRouterMode
          ? window.open(`/#${routePath}`)
          : window.open(routePath)
    } else {
      if (isExternal(routePath)) window.location.href = routePath
      else if (route.path !== routePath) {
        if (device.value === 'mobile') foldSideBar()
        // 匹配下级路由，如果存在Index主页，直接跳转到该路由
        router.push(
          props.itemOrMenu.childrenPathList.length > 0 &&
            props.itemOrMenu.childrenPathList[0].indexOf('/index') > -1
            ? props.itemOrMenu.childrenPathList[0]
            : props.itemOrMenu.path
        )
      } else {
        $pub('reload-router-view')
      }
    }
  }
</script>

<template>
  <!--判断首页是否选中，暂只能这样，除非是改成tab模式，即路由和path是一致的-->
  <el-menu-item
    :index="itemOrMenu.path"
    @click="handleLink"
    :class="[
      route.path === itemOrMenu.path ||
      (itemOrMenu.path.indexOf('/index') > -1 &&
        route.path.indexOf('/index') > -1)
        ? 'is-active'
        : '',
    ]"
  >
    <vab-icon
      v-if="itemOrMenu.meta.icon"
      :icon="itemOrMenu.meta.icon"
      :is-custom-svg="true"
      :title="itemOrMenu.meta.title"
      class="custom-size"
    />
    <span :title="itemOrMenu.meta.title">
      {{ itemOrMenu.meta.title }}
    </span>
    <el-tag v-if="itemOrMenu.meta.badge" effect="dark" type="danger">
      {{ itemOrMenu.meta.badge }}
    </el-tag>
    <span v-if="itemOrMenu.meta.dot" class="vab-dot vab-dot-error">
      <span />
    </span>
  </el-menu-item>
</template>

<style lang="scss" scoped>
  @use 'sass:math';
  :deep(.el-tag) {
    position: absolute;
    right: 20px;
    height: 16px;
    padding-right: 4px;
    padding-left: 4px;
    // margin-top: #{math.div($base-menu-item-height - 16, 2)};
    line-height: 16px;
    border: 0;
  }

  .el-menu-item {
    &:hover {
      background-color: $base-color-hover;
    }
    &.is-active {
      font-weight: 600;
      color: #3977f3;
      background-color: $base-color-hover;
    }
  }

  .vab-dot {
    position: absolute !important;
    right: 20px;
    // margin-top: #{math.div($base-menu-item-height - 6, 2)};
  }
</style>
