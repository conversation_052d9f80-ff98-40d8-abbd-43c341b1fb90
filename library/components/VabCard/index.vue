<script lang="ts" setup>
  import { CSSProperties } from 'vue'
  import { onBeforeRouteLeave } from 'vue-router'
  import type { CardProps } from 'element-plus'
  type ShadowType = CardProps['shadow']
  defineProps<{
    header?: string | ''
    bodyStyle?: CSSProperties | undefined
    shadow?: ShadowType
    skeleton?: boolean | false
    skeletonRows?: number | 4
  }>()

  let timer: any = null
  const skeletonShow = ref(true)

  timer = setTimeout(() => {
    skeletonShow.value = false
  }, 500)

  onBeforeRouteLeave((to, from, next) => {
    clearInterval(timer)
    next()
  })
</script>

<template>
  <el-card :body-style="bodyStyle" class="vab-card" :shadow="shadow">
    <template v-if="$slots.header || header" #header>
      <slot name="header">{{ header }}</slot>
    </template>
    <el-skeleton
      v-if="skeleton"
      animated
      :loading="skeletonShow"
      :rows="skeletonRows"
    >
      <template #default>
        <slot class="vab-card-transition" />
      </template>
    </el-skeleton>
    <slot v-else class="vab-card-transition" />
  </el-card>
</template>

<style lang="scss" scoped>
  .vab-card {
    &-transition {
      transition: $base-transition;
    }
  }
</style>
