<script lang="ts" setup>
  import { useUserStore } from '@/store/modules/user'
  import { toLoginRoute } from '@/utils/routes'
  import { VabRoute } from '/#/router'
  import CommonIcon from '~/src/components/CommonIcon.vue'
  import { IconType } from '~/src/icon'
  import { useSettingsStore } from '~/src/store/modules/settings'

  const route: VabRoute = useRoute()
  const router = useRouter()

  const userStore = useUserStore()
  const settingsStore = useSettingsStore()
  const { collapse } = storeToRefs(settingsStore)
  const { avatar, username, email } = storeToRefs(userStore)
  const { logout } = userStore

  const handleLogout = async () => {
    await logout()
    await router.push(toLoginRoute(route.fullPath))
  }
  const handleToPersonalCenter = () => {
    router.push('/personalCenter')
  }
</script>

<template>
  <div
    class="user-box"
    :style="{
      justifyContent: collapse ? 'center' : 'space-between',
    }"
  >
    <div
      class="avatar-dropdown"
      :style="{
        padding: collapse ? '20px 10px' : '20px 23px',
      }"
      @click="handleToPersonalCenter"
    >
      <div><el-avatar class="user-avatar" :src="avatar" /></div>
      <div v-if="!collapse" class="user-name">
        <div class="hidden-xs-only">{{ username }}</div>
        <div>{{ email }}</div>
      </div>
    </div>

    <el-tooltip effect="dark" content="退出登录" placement="right">
      <div v-if="!collapse" class="log-out" @click="handleLogout">
        <common-icon :type="IconType.Exit" :size="30" />
      </div>
    </el-tooltip>
  </div>
</template>

<style lang="scss" scoped>
  .user-box {
    display: flex;
    align-items: center;
    width: 100%;
    border-top: 1px solid #e5e7eb;
  }
  .avatar-dropdown {
    display: flex;
    align-content: center;
    align-items: center;
    cursor: pointer;

    .user-avatar {
      flex-shrink: 0;
      width: 40px;
      height: 40px;
      cursor: pointer;
      border-radius: 50%;
    }

    .user-name {
      position: relative;
      margin-left: 6px;
      div {
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      [class*='ri-'] {
        margin-left: 3px !important;
      }

      div,
      i {
        color: $base-menu-color;
      }
    }
  }
  .log-out {
    padding: 8px;
    color: #999;
    cursor: pointer;
    &:hover {
      color: $base-color-primary;
    }
  }
</style>
