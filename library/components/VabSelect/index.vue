<template>
  <el-select
    ref="selectRef"
    v-model="selectVals"
    class="m-2 status-label"
    :class="tagClass"
    :clearable="clearable"
    collapse-tags
    :disabled="disabled"
    multiple
    :placeholder="placeholder"
    @change="handleChange"
  >
    <!-- <template #prefix>233444</template> -->
    <el-option
      v-for="item in option"
      :key="item.value"
      class="tag-option"
      :label="item.label"
      :value="item.value"
    >
      <el-tag class="ml-2" :type="item.className || 'primary'">
        <span>
          {{ item.label }}
        </span>
      </el-tag>
    </el-option>
  </el-select>
</template>

<script lang="ts" setup>
  import type { TagProps } from 'element-plus'
  type TagType = TagProps['type']
  type Options = {
    value: any
    label: string
    className: TagType
  }
  const props = defineProps({
    disabled: {
      type: Boolean,
      default: false,
    },
    clearable: {
      type: Boolean,
      default: false,
    },
    value: {
      type: String,
      default: '',
    },
    option: {
      type: Array<Options>,
      default: () => {
        return []
      },
    },
    placeholder: {
      type: String,
      default: '请选择',
    },
  })
  const { option, value, disabled, clearable } = toRefs(props)

  const temp = option.value.find((item) => {
    return value.value === item.value
  })

  const tagClass = ref(temp?.className)
  const selectVals: any = ref(
    value.value
      ? value.value.split(',').map((item) => {
          return Number(item)
        })
      : []
  )

  // 外部值改变时重新赋值
  watch(value, (v) => {
    selectVals.value = v
      ? v.split(',').map((item) => {
          return Number(item)
        })
      : []
  })

  const selectRef: any = ref(null)
  const emit = defineEmits(['update:value', 'change'])
  const handleChange = (v: Array<any>) => {
    // if (v.length === 0) {
    //   selectVals.value.length = 0
    //   emit('update:value', null)
    //   emit('change', null)
    //   return
    // }
    // const lastValue: any = v[v.length - 1]
    // const temp = option.value.find((item) => {
    //   return lastValue === item.value
    // })
    // if (temp) {
    //   tagClass.value = temp?.className
    // }
    // selectVals.value.length = 0
    // selectVals.value.push(lastValue)
    // selectRef.value.blur() //隐藏下拉
    emit('update:value', v.join(','))
    emit('change', v.join(','))
  }

  nextTick(() => {
    selectVals.value.length = 0
    if (!value.value) {
      return
    }
    selectVals.value = value.value.split(',').map((item) => {
      return Number(item)
    })
  })
</script>
