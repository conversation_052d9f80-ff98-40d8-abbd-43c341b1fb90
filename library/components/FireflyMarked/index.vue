<template>
  <div class="fireflymark-content">
    <div
      v-if="value"
      class="vditor-reset article-content aie-theme-light"
      :id="id"
      v-html="state.html"
    ></div>
    <div>
      <el-image-viewer
        v-if="state.showImage"
        @close="closeViewer"
        :initial-index="state.imgIndex"
        :url-list="state.srcList"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
  import hljs from 'highlight.js'
  import { onMounted, onBeforeUpdate } from 'vue'
  import VditorPreview from 'vditor/dist/method.min'
  import '../../../node_modules/aieditor/dist/style.css'
  import { marked } from 'marked'
  import { vi } from 'element-plus/es/locale'

  const emit = defineEmits(['image-show-status-change'])

  const props = defineProps({
    id: {
      type: String,
      default: 'html-value',
    },
    value: {
      type: String,
      default: '',
    },
    showType: {
      type: String,
      default: 'mark',
    },
  })

  const { value, id } = toRefs(props)

  // 正则匹配md文本中html url链接将其置为标准 []() 超链接形式
  const mdHttpChange = (value: string) => {
    // return value
    // // 匹配http开头 负向断言之前无 [ 与 ( 防止改变原有标准链接，在为空处或者为中文亦或中文括号处结束
    const regex = /(?<![[(])https?:\/\/[^\s\u4e00-\u9fa5（）]+/g
    const matches: any = value.match(regex)

    // 视频保存时候格式类似<source src="http://172.16.10.239:8063/files/2024/03/240318024729_bunny1.mp4">
    const video = ['mp4">', 'avi">', 'mov">', 'mkv">']

    let mdValue = value
    if (matches) {
      matches.forEach((item: string) => {
        if (!video.includes(item.split('.').pop()?.toLowerCase() ?? '')) {
          mdValue = mdValue.replace(item, `[${item}](${item})`)
        }
      })
    }
    return mdValue
  }

  /**
   * 处理图片排版问题
   * @param content
   */
  const dealHtmlContent = (content: string) => {
    // 匹配img标签中align属性为left或right的
    const imgAlignRegex = /(<img[^>]*)(align="(left|right|center)")([^>]*>)/g
    const matches = content.matchAll(imgAlignRegex)
    let modifiedContent = content
    if (matches) {
      for (const match of matches) {
        const fullMatch = match[0]
        const alignValue = match[3]
        let replacedItem
        if (alignValue == 'center') {
          replacedItem = fullMatch.replace(
            'align="' + alignValue + '"',
            'style="margin: 0 auto;display: block"'
          )
        } else {
          replacedItem = fullMatch.replace(
            'align="' + alignValue + '"',
            'style="display: block;margin: auto; margin-' +
              (alignValue === 'left' ? 'left' : 'right') +
              ': 0px"'
          )
        }
        modifiedContent = modifiedContent.replace(fullMatch, replacedItem)
      }
    }
    return modifiedContent ?? content
  }

  const state = reactive({
    showImage: false,
    imgIndex: 0,
    srcList: [] as Array<string>,
    html:
      props.showType == 'mark'
        ? mdHttpChange(value.value)
        : dealHtmlContent(value.value),
    options: {},
  })
  const cdn =
    process.env.VUE_APP_VDITOR_CDN ||
    'https://download.t-firefly.com/Source/vditor'

  watch(value, async (v) => {
    // 需要await 否则此步慢半拍导致最后更新的那份数据未能被渲染处理
    state.html =
      props.showType == 'mark' ? await mdHttpChange(v) : dealHtmlContent(v)
    upMarkedValue()
  })

  /**
   * 传入内容为html格式下 使高亮代码块
   */
  const highlightCodeBlocks = () => {
    const previewElement = document.getElementById(props.id)
    if (previewElement) {
      previewElement.querySelectorAll('pre code').forEach((block) => {
        hljs.highlightElement(block as HTMLElement)
      })
    }
  }

  const upMarkedValue = async () => {
    const previewElement = document.getElementById(props.id)
    if (previewElement) {
      await VditorPreview.highlightRender('', previewElement, cdn)
      await VditorPreview.codeRender(previewElement)
      // 显示模式
      if (props.showType == 'mark') {
        // vditor 的 preview 存在问题：对传入的html内容里的代码可能会自动注释或者样式错乱
        await VditorPreview.preview(previewElement, state.html, {
          cdn: cdn,
          math: {
            engine: 'MathJax',
          },
          theme: {
            path: 'https://download.t-firefly.com/Source/vditor/dist/css/content-theme',
          },
        })
      } else {
        highlightCodeBlocks()
      }
    }
  }

  const closeViewer = () => {
    state.showImage = false
    emit('image-show-status-change', false)
  }

  onMounted(async () => {
    state.options = {
      cdn: cdn,
    }
    if (props.value) {
      // state.html = mdHttpChange(value.value)
      // state.html = await VditorPreview.md2html(state.html, state.options)
      await upMarkedValue()
    }

    // 监听图片点击事件
    let imgs = document.getElementById(props.id)?.querySelectorAll('img')
    if (imgs) {
      state.srcList.length = 0
      imgs.forEach((item: any, key) => {
        state.srcList.push(item.src)
        item.addEventListener('click', (e: any) => {
          state.imgIndex = key
          state.showImage = true
          emit('image-show-status-change', true)
        })
      })
    }
  })
</script>

<style lang="scss">
  .vditor-reset h1,
  .vditor-reset h2,
  .vditor-reset h3,
  .vditor-reset h4,
  .vditor-reset h5,
  .vditor-reset h6 {
    font-size: revert !important;
  }
  .vditor-reset {
    table {
      display: table !important;
      table-layout: fixed;
      overflow: hidden;
      td,
      th {
        white-space: break-spaces !important;
        word-wrap: break-word;
        word-break: break-all !important;
      }
    }
  }
</style>

<style lang="scss" scoped>
  .vditor-reset {
    width: 100%;
  }
  :deep() {
    a > strong > span {
      color: #116699 !important; // 修改加粗厚的标题为链接时的颜色
    }
    a {
      color: #116699 !important;
    }
    .hljs {
      background: #ececed !important;
    }
    .mention {
      color: $base-color-primary !important;
    }
    .vditor-reset {
      img {
        display: block !important;
      }
    }
    .vditor-reset h1,
    .vditor-reset h2,
    .vditor-reset h3,
    .vditor-reset h4,
    .vditor-reset h5,
    .vditor-reset h6 {
      font-size: revert;
    }
  }

  .vditor-reset {
    img {
      display: block !important;
    }
  }
</style>
