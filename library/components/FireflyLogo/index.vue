<script lang="ts" setup>
  import { useSettingsStore } from '@/store/modules/settings'

  const settingsStore = useSettingsStore()
  const { theme, logo, title, collapse } = storeToRefs(settingsStore)
</script>

<template>
  <div
    class="logo-container"
    :class="{
      ['logo-container-' + theme.layout]: true,
    }"
    :style="{
      paddingLeft: collapse ? '0px' : '16px',
      justifyContent: collapse ? 'center' : 'left',
    }"
  >
    <span v-if="logo" class="logo">
      <vab-icon icon="logo" is-custom-svg />
    </span>
    <span v-if="!collapse" class="title">
      {{ title }}
    </span>
  </div>
</template>

<style lang="scss" scoped>
  @mixin container {
    position: relative;
    height: $base-header-height;
    overflow: hidden;
    line-height: $base-header-height;
    background: transparent;
  }

  @mixin logo {
    display: inline-block;
    width: 35px;
    height: 35px;
    color: $base-title-color;
    vertical-align: middle;
    fill: currentColor;
  }

  @mixin title {
    display: inline-block;
    margin-left: 13px;
    overflow: hidden;
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: 55px;
    color: #3977f3;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
  }

  .logo-container {
    display: flex;
    align-items: center;
    &-horizontal,
    &-common {
      @include container;

      .logo {
        svg,
        img {
          @include logo;
        }
      }

      .title {
        @include title;
      }
    }

    &-vertical,
    &-column,
    &-comprehensive,
    &-float {
      @include container;

      height: $base-logo-height;
      line-height: $base-logo-height;
      text-align: center;

      .logo {
        svg,
        img {
          @include logo;
        }
      }

      .title {
        @include title;
        max-width: calc(var(--el-left-menu-width) - 60px);
      }
    }

    &-column {
      background: $base-column-second-menu-background !important;

      .logo {
        position: fixed;
        top: 0;
        display: block;
        width: $base-left-menu-width-min;
        height: $base-logo-height;
        margin: 0;
        background: $base-column-first-menu-background;
      }

      .title {
        padding-right: 15px;
        padding-left: 15px;
        margin-left: $base-left-menu-width-min !important;
        color: var(--el-color-black) !important;
        background: $base-column-second-menu-background !important;
        @include title;
      }
    }
  }
</style>
