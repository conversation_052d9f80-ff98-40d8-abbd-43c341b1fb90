<template>
  <div class="page-box">
    <!-- 搜索选项 -->

    <el-form
      v-if="!!search"
      ref="searchForm"
      class="search"
      :inline="true"
      label-position="left"
      :label-width="search.labelWidth"
      :model="searchModel"
    >
      <el-form-item
        v-for="item in search.fields"
        :key="item.name"
        :label="item.label"
        :prop="item.name"
      >
        <slot v-if="item.type === 'custom'" :name="item.slot" />
        <el-select
          v-else-if="item.type === 'select'"
          v-model="searchModel[item.name]"
          clearable
          :filterable="!!item.filterable"
          :multiple="!!item.multiple"
          :placeholder="`请选择${item.label}`"
          :style="{ width: search.inputWidth, ...item.style }"
        >
          <el-option
            v-for="option of item.options"
            :key="option.value"
            :label="option.name"
            :value="option.value"
          />
        </el-select>
        <el-radio-group
          v-else-if="item.type === 'radio'"
          v-model="searchModel[item.name]"
          :style="{ width: search.inputWidth, ...item.style }"
        >
          <el-radio
            v-for="option of item.options"
            :key="option.value"
            :label="option.value"
          >
            {{ option.name }}
          </el-radio>
        </el-radio-group>
        <el-radio-group
          v-else-if="item.type === 'radio-button'"
          v-model="searchModel[item.name]"
          :style="{ width: search.inputWidth, ...item.style }"
        >
          <el-radio-button
            v-for="option of item.options"
            :key="option.value"
            :label="option.value"
          >
            {{ option.name }}
          </el-radio-button>
        </el-radio-group>
        <el-checkbox-group
          v-else-if="item.type === 'checkbox'"
          v-model="searchModel[item.name]"
          :style="{ width: search.inputWidth, ...item.style }"
        >
          <el-checkbox
            v-for="option of item.options"
            :key="option.value"
            :label="option.value"
          >
            {{ option.name }}
          </el-checkbox>
        </el-checkbox-group>
        <el-checkbox-group
          v-else-if="item.type === 'checkbox-button'"
          v-model="searchModel[item.name]"
          :style="{ width: search.inputWidth, ...item.style }"
        >
          <el-checkbox-button
            v-for="option of item.options"
            :key="option.value"
            :label="option.value"
          >
            {{ option.name }}
          </el-checkbox-button>
        </el-checkbox-group>
        <el-date-picker
          v-else-if="item.type === 'date'"
          v-model="searchModel[item.name]"
          clearable
          format="YYYY-MM-DD"
          :placeholder="`请选择${item.label}`"
          :style="{ width: search.inputWidth, ...item.style }"
          type="date"
          @change="handleDateChange($event, item, 'YYYY-MM-DD')"
        />
        <el-date-picker
          v-else-if="item.type === 'datetime'"
          v-model="searchModel[item.name]"
          clearable
          format="YYYY-MM-DD HH:mm:ss"
          :placeholder="`请选择${item.label}`"
          :style="{ width: search.inputWidth, ...item.style }"
          type="datetime"
          @change="handleDateChange($event, item, 'YYYY-MM-DD HH:mm:ss')"
        />
        <el-date-picker
          v-else-if="item.type === 'daterange'"
          v-model="searchModel[item.name]"
          clearable
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          range-separator="-"
          start-placeholder="开始日期"
          :style="{ width: search.inputWidth, ...item.style }"
          type="daterange"
          @change="handleRangeChange($event, item, 'YYYY-MM-DD')"
        />
        <el-date-picker
          v-else-if="item.type === 'datetimerange'"
          v-model="searchModel[item.name]"
          clearable
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          range-separator="-"
          start-placeholder="开始时间"
          :style="{ width: search.inputWidth, ...item.style }"
          type="datetimerange"
          @change="handleRangeChange($event, item, 'YYYY-MM-DD HH:mm:ss')"
        />
        <el-input-number
          v-else-if="item.type === 'number'"
          v-model="searchModel[item.name]"
          controls-position="right"
          :max="item.max"
          :min="item.min"
          :placeholder="`请输入${item.label}`"
          :style="{ width: search.inputWidth, ...item.style }"
        />
        <el-input
          v-else-if="item.type === 'textarea'"
          v-model="searchModel[item.name]"
          clearable
          :placeholder="`请输入${item.label}`"
          :style="{ width: search.inputWidth, ...item.style }"
          type="textarea"
        />
        <el-input
          v-else
          v-model="searchModel[item.name]"
          clearable
          :placeholder="`请输入${item.label}`"
          :style="{ width: search.inputWidth, ...item.style }"
        />
      </el-form-item>
      <el-form-item class="search-btn">
        <el-button icon="el-icon-search" type="primary" @click="handleSearch">
          查询
        </el-button>
        <el-button icon="el-icon-refresh-right" @click="handleReset">
          重置
        </el-button>
      </el-form-item>
    </el-form>

    <!-- title 和 工具栏 -->
    <div v-if="!hideTitleBar" class="head">
      <slot name="title">
        <span class="title">{{ title }}</span>
      </slot>
      <div class="toolbar">
        <slot name="toolbar"></slot>
      </div>
    </div>
    <!-- table表格栏 -->
    <div class="table">
      <el-table
        ref="table"
        v-loading="loading"
        :height="height"
        :max-height="maxHeight"
        :span-method="spanMethod"
        border
        :data="tableData"
        :lazy="tree.lazy"
        :load="tree.load"
        :row-key="rowKey"
        tooltip-effect="dark"
        :tree-props="tree.treeProps"
        :default-expand-all="defaultExpandAll"
        @selection-change="handleSelectionChange"
        @cell-click="handleCellClick"
        @cell-dblclick="handleCellDblclick"
        @sort-change="handleSortChange"
      >
        <el-table-column
          v-for="item in columns"
          :key="item.label"
          :filter-method="item.filters && filterHandler"
          :show-overflow-tooltip="!item.wrap"
          :sortable="item.sortable"
          v-bind="item"
        >
          <template #header="scope" v-if="!!item.labelSlot">
            <slot :name="item.labelSlot" v-bind="scope"></slot>
          </template>
          <template #default="scope" v-if="!!item.tdSlot">
            <slot :name="item.tdSlot" v-bind="scope"></slot>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页 -->
    <el-pagination
      v-if="paginationConfig.show && total > 0"
      v-model:currentPage="pageNum"
      v-model:pageSize="pageSize"
      background
      class="pagination"
      :layout="paginationConfig.layout"
      :page-sizes="paginationConfig.pageSizes"
      :style="paginationConfig.style"
      :total="total"
      @current-change="handleCurrentChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>
<script>
  import { defineComponent, reactive, toRefs, onBeforeMount } from 'vue'
  const formatDate = (date, format) => {
    let obj = {
      'M+': date.getMonth() + 1,
      'D+': date.getDate(),
      'H+': date.getHours(),
      'm+': date.getMinutes(),
      's+': date.getSeconds(),
      'q+': Math.floor((date.getMonth() + 3) / 3),
      'S+': date.getMilliseconds(),
    }
    if (/(y+)/i.test(format)) {
      format = format.replace(
        RegExp.$1,
        (date.getFullYear() + '').substr(4 - RegExp.$1.length)
      )
    }
    for (let k in obj) {
      if (new RegExp('(' + k + ')').test(format)) {
        format = format.replace(
          RegExp.$1,
          RegExp.$1.length == 1
            ? obj[k]
            : ('00' + obj[k]).substr(('' + obj[k]).length)
        )
      }
    }
    return format
  }
  const getSearchModel = (search) => {
    const searchModel = {}
    if (search && search.fields) {
      search.fields.forEach((item) => {
        switch (item.type) {
          case 'checkbox':
          case 'checkbox-button':
            searchModel[item.name] = []
            break
          default:
            break
        }
        if (item.defaultValue !== undefined) {
          searchModel[item.name] = item.defaultValue
          // 日期范围和时间范围真实变量默认值
          if (
            (item.type === 'daterange' || item.type === 'datetimerange') &&
            !!item.trueNames &&
            Array.isArray(item.defaultValue)
          ) {
            item.defaultValue.forEach((val, index) => {
              searchModel[item.trueNames[index]] = val
            })
          }
        }
      })
    }
    return searchModel
  }
  export default defineComponent({
    props: {
      // 请求数据的方法
      request: {
        type: Function,
        default: () => {},
      },
      // 表格标题
      title: {
        type: String,
        default: '',
      },
      // 是否隐藏标题栏
      hideTitleBar: {
        type: Boolean,
        default: true,
      },
      // 搜索表单配置，false表示不显示搜索表单
      search: {
        type: [Boolean, Object],
        default: false,
      },
      border: {
        type: Boolean,
        default: true,
      },
      // 表头配置
      columns: {
        type: Array,
        default: function () {
          return []
        },
      },
      // 行数据的Key，同elementUI的table组件的row-key
      rowKey: {
        type: String,
        default: 'id',
      },
      // 分页配置，false表示不显示分页
      pagination: {
        type: [Boolean, Object],
        default: () => ({}),
      },
      tree: {
        type: Object,
        default: () => ({}),
      },
      //是否默认展开所有行
      defaultExpandAll: {
        type: Boolean,
        default: false,
      },
      height: {
        type: [String, Number],
        default: null,
      },
      maxHeight: {
        type: [String, Number],
        default: null,
      },
      spanMethod: {
        type: Function,
        default: null,
      },
      stripe: {
        type: Boolean,
        default: true,
      },
      useSerialNumber: {
        type: Boolean,
        default: false,
      },
    },
    emits: ['selectionChange', 'cell-click', 'cell-dblclick', 'sort-change'],
    setup(props, { emit }) {
      const state = reactive({
        searchModel: getSearchModel(props.search),
        loading: false,
        tableData: [],
        total: 0,
        pageNum: 1,
        pageSize: (!!props.pagination && props.pagination.pageSize) || 15,
        paginationConfig: {
          show: false,
        },
        // 搜索
        handleSearch() {
          state.pageNum = 1
          getTableData()
        },
        // 重置函数
        handleReset() {
          if (JSON.stringify(state.searchModel) === '{}') {
            return
          }
          state.pageNum = 1
          state.searchModel = getSearchModel(props.search)
          getTableData()
        },
        // 刷新
        refresh() {
          getTableData()
        },

        // 当前页变化
        handleCurrentChange(page) {
          getTableData()
        },
        // 改变每页size数量
        handleSizeChange(value) {
          state.pageNum = 1
          getTableData()
        },
        // 全选
        handleSelectionChange(arr) {
          emit('selectionChange', arr)
        },
        //点击行
        handleCellClick(row) {
          emit('cell-click', row)
        },
        //双击行
        handleCellDblclick(row) {
          emit('cell-dblclick', row)
        },
        //自定义排序
        handleSortChange(opt) {
          emit('sort-change', opt)
        },
        // 过滤方法
        filterHandler(value, row, column) {
          const property = column['property']
          return row[property] === value
        },
        // 日期范围
        handleDateChange(date, item, format) {
          state.searchModel[item.name] = date ? formatDate(date, format) : ''
        },
        handleRangeChange(date, item, format) {
          const arr = !!date && date.map((d) => formatDate(d, format))
          state.searchModel[item.name] = arr ? arr : []

          if (!item.trueNames) {
            return
          }

          if (arr) {
            arr.forEach((val, index) => {
              state.searchModel[item.trueNames[index]] = val
            })
          } else {
            item.trueNames.forEach((key) => {
              delete state.searchModel[key]
            })
          }
        },
      })
      // 优化搜索字段，
      // 1、如果搜索配置有transform处理函数，执行transform
      // 2、删除日期范围默认的name字段
      const optimizeFields = (search) => {
        const searchModel = JSON.parse(JSON.stringify(state.searchModel))
        if (search && search.fields) {
          search.fields.forEach((item) => {
            // eslint-disable-next-line no-prototype-builtins
            if (!searchModel.hasOwnProperty(item.name)) {
              return
            }
            if (item.transform) {
              searchModel[item.name] = item.transform(searchModel[item.name])
            }
            if (
              (item.type === 'daterange' || item.type === 'datetimerange') &&
              !!item.trueNames
            ) {
              delete searchModel[item.name]
            }
          })
        }
        return searchModel
      }

      // 请求列表数据
      const getTableData = async () => {
        state.loading = true
        const searchModel = optimizeFields(props.search)
        const { data, total } = await props.request({
          pageNo: state.pageNum,
          limit: props.pagination === false ? null : state.pageSize,
          ...searchModel,
        })
        state.loading = false
        state.tableData = data
        if (props.useSerialNumber) {
          state.tableData.forEach((item, index) => {
            item.serial_number = index + 1
          })
        }
        state.total = total
      }

      onBeforeMount(() => {
        getTableData()
      })

      if (typeof props.pagination === 'object') {
        // eslint-disable-next-line vue/no-setup-props-destructure
        const { layout, pageSizes, style } = props.pagination
        state.paginationConfig = {
          show: true,
          layout: layout || 'total, sizes, prev, pager, next',
          pageSizes: pageSizes || [15, 30, 50, 100],
          style: style || {},
        }
      }

      return {
        ...toRefs(state),
      }
    },
  })
</script>
<style lang="scss" scoped>
  .page-box {
    box-sizing: border-box;
    width: 100%;
    .search {
      display: flex;
      flex-wrap: wrap;
      padding: 20px 20px 0;
      margin-bottom: 10px;
      background: #fff;
      .el-form-item {
        margin-bottom: 20px;
      }
      .search-btn {
        margin-left: auto;
      }
      :deep(.el-input-number .el-input__inner) {
        text-align: left;
      }
    }
    .head {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px 20px 0;
      background: #fff;
      .title {
        font-size: 16px;
      }
    }

    .pagination {
      padding: 0 20px;
      text-align: right;
      background: #fff;
      :last-child {
        margin-right: 0;
      }
    }
  }

  .table {
    :deep(.el-table__cell) {
      // border-right: none;
    }
    :deep(.el-table) {
      border-radius: 6px 6px 0px 0px;
      th .cell {
        color: #9199a3;
      }
    }
  }
</style>
