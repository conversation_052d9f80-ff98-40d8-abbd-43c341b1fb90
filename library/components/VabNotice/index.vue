<script lang="ts" setup>
  import { useSettingsStore } from '@/store/modules/settings'
  import { toggleClass } from 'dom7'
  import { translate } from '@/i18n'
  import { getList } from '@/api/notice'

  const $baseMessage: any = inject('$baseMessage')

  const settingsStore = useSettingsStore()
  const { theme } = storeToRefs(settingsStore)

  const activeName = ref('notice')
  const notices: any = ref([
    {
      type: 1,
      title: '系统升级通知',
      time: '2022-10-09',
      content: '3月周报尚未填写，将会影响你的绩效考核，请及时处理！',
    },
    {
      type: 2,
      title: '周报提醒',
      time: '2022-10-09',
      content:
        '尊敬的淘宝会员：t_firefly，您好！ 您已成功订购：淘宝服务平台_爱用交易_初级版，服务到期时间：2023-09-01。 查看您已订购的所有服务：',
    },
    {
      type: 3,
      title: '每日销售额',
      time: '2022-10-09',
      content: '3月周报尚未填写，将会影响你的绩效考核，请及时处理！',
    },
  ])
  const badge = ref(12)

  const fetchData = async () => {
    const {
      data: { list, total },
    } = await getList()
    notices.value = list
    badge.value = total === 0 ? undefined : total
  }

  nextTick(() => {
    // if (theme.value.showNotice) fetchData()
  })

  const handleClick = () => {
    fetchData()
  }

  const handleClearNotice = () => {
    badge.value = 0
    notices.value = []
    $baseMessage('清空消息成功', 'success', 'vab-hey-message-success')
  }

  const drawerVisible = ref(false)

  //切换显示设置列表
  const value = ref(false)
  const showSubscribeSetting = ref(false) //是否显示订阅设置
  const toggleShowSetList = () => {
    showSubscribeSetting.value = !showSubscribeSetting.value
  }
</script>

<template>
  <el-badge v-if="theme.showNotice" type="danger" :value="badge ? badge : ''">
    <div style="cursor: pointer" @click="drawerVisible = true">
      <vab-icon icon="notification-line" />
      <span>通知</span>
    </div>

    <el-drawer v-model="drawerVisible" direction="rtl">
      <template #header>
        <div class="notice-head">
          <div class="head-box">
            <el-divider
              direction="vertical"
              style="border-color: #3977f3; border-width: 2px"
            />
            <span class="text">消息</span>

            <el-button text class="btn">
              <vab-icon icon="brush-3-fill" />
              清除消息
            </el-button>
          </div>

          <el-tooltip content="点击设置订阅">
            <el-button text class="btn" @click="toggleShowSetList">
              <vab-icon icon="folder-settings-fill" class="primary-icon" />
            </el-button>
          </el-tooltip>
        </div>
      </template>
      <div v-if="!showSubscribeSetting" class="notice-list">
        <ul>
          <li v-for="(item, index) in notices" :key="index">
            <div class="head">
              <div class="title-left">
                <img
                  :src="require(`@/assets/icon_images/notice/${item.type}.png`)"
                  class="notice-icon"
                />
                <span>{{ item.title }}</span>
              </div>

              <div class="icon-right">
                <div class="badge"></div>
                <vab-icon icon="arrow-right-s-line" class="arrow-right" />
              </div>
            </div>
            <div class="content">
              {{ item.content }}
            </div>
            <div class="time">
              {{ item.time }}
            </div>
          </li>
        </ul>
      </div>

      <div v-else class="subscribe-list">
        <div class="subscribe-item">
          <span>周报提醒</span>
          <el-switch v-model="value" size="large" />
        </div>

        <div class="subscribe-item">
          <span>每日销售额</span>
          <el-switch v-model="value" size="large" />
        </div>

        <div class="subscribe-item">
          <span>系统公告</span>
          <el-switch v-model="value" size="large" />
        </div>
      </div>
    </el-drawer>
  </el-badge>
</template>

<style lang="scss" scoped>
  :deep() {
    .el-tabs__active-bar {
      min-width: 28px;
    }
  }

  :deep(.el-drawer__header) {
    margin-bottom: 0px;
  }

  .notice-head {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .head-box {
      display: flex;
      align-items: center;
    }
    .text {
      font-size: 16px;
      font-weight: bold;
      color: #333;
    }
    .btn {
      margin-left: 10px;
      &:hover {
        color: #3977f3;
      }
    }
  }

  .notice-list {
    ul {
      padding: 0 15px 0 0;
      margin: 0;
      overflow: auto;

      li {
        padding: 20px 10px 10px 10px;
        cursor: pointer;
        border-top: 1px solid #f2f4f6;
        border-radius: 10px;
        &:hover {
          background: #eee;
        }

        .notice-icon {
          width: 24px;
          height: 24px;
        }

        .head {
          display: flex;
          align-items: center;
          justify-content: space-between;
          color: #333;

          .title-left {
            display: flex;
            align-items: center;

            span {
              margin-left: 10px;
              font-size: 16px;
            }
          }

          .icon-right {
            display: flex;
            align-items: center;
            .badge {
              width: 8px;
              height: 8px;
              background: #ff3434;
              border-radius: 50%;
            }
          }
          .arrow-right {
            font-size: 20px;
            color: #ccc;
          }
        }
        .content {
          margin: 15px 0px 10px 0px;
          font-size: 14px;
          color: #666666;
        }
        .time {
          font-size: 12px;
          color: #ccc;
        }

        span {
          margin-left: 10px;
        }
      }
    }
  }

  .subscribe-list {
    .subscribe-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 20px;
      border-radius: 5px;
      &:hover {
        background: #eee;
      }
      & > span {
        color: #333;
      }
    }
  }
</style>
